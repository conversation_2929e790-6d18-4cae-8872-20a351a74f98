import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get_it/get_it.dart';
import 'Dashboard/dashboard_screen.dart';
import 'theme/app_theme.dart';
import 'services/logging_service.dart';
import 'services/database/isar_initializer.dart';
import 'routes/app_routes.dart';
import 'Dashboard/Reports/screens/transactions_report_screen.dart';
import 'Dashboard/Reports/screens/milk_report_screen.dart';
import 'Dashboard/Reports/screens/events_report_screen.dart';
import 'Dashboard/Reports/screens/breeding_report_screen.dart';
import 'Dashboard/Reports/screens/cattle_report_screen.dart';
import 'Dashboard/Cattle/screens/cattle_screen.dart';
import 'Dashboard/Breeding/screens/breeding_screen.dart';
import 'Dashboard/Health/screens/health_screen.dart';
import 'Dashboard/Milk Records/screens/milk_screen.dart';
import 'Dashboard/Transactions/screens/transactions_screen.dart';
import 'Dashboard/Farm Setup/screens/farm_setup_screen.dart';
import 'Dashboard/Reports/screens/weight_report_screen.dart';
import 'Dashboard/Reports/screens/pregnancies_report_screen.dart';
import 'Dashboard/Reports/screens/health_report_screen.dart';
import 'Dashboard/Farm Setup/services/farm_setup_handler.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Logging Service
  final loggingService = LoggingService();
  loggingService.setupLogging();
  loggingService.logUncaughtExceptions();

  // Initialize Isar database
  bool isarInitialized = false;
  final getIt = GetIt.instance;

  try {
    // Initialize Isar
    loggingService.info("Initializing Isar database");
    await IsarInitializer.initialize();
    isarInitialized = true;
    loggingService.info("Isar initialization succeeded");

    if (isarInitialized) {
      // Ensure default data exists
      await IsarInitializer.ensureDefaultData();

      // Get handlers from GetIt
      final farmSetupHandler = getIt<FarmSetupHandler>();

      // Log database information for debugging
      final animalTypes = await farmSetupHandler.getAllAnimalTypes();
      loggingService
          .info('Loaded ${animalTypes.length} animal types from Isar');

      final breedCategories = await farmSetupHandler.getAllBreedCategories();
      loggingService
          .info('Loaded ${breedCategories.length} breed categories from Isar');

      // Check if breeds are missing and initialize them if needed
      if (breedCategories.isEmpty && animalTypes.isNotEmpty) {
        loggingService.info('No breeds found but animal types exist. Initializing default breeds...');
        await IsarInitializer.initializeDefaultBreeds();

        // Check again after initialization
        final updatedBreeds = await farmSetupHandler.getAllBreedCategories();
        loggingService.info('After initialization: ${updatedBreeds.length} breed categories loaded');
      }

      final farms = await farmSetupHandler.getAllFarms();
      if (farms.isNotEmpty) {
        loggingService.info('Loaded farm: ${farms.first.name}');
      } else {
        loggingService.info('No farm found in database');
      }
    }
  } catch (e) {
    loggingService.error("Error initializing Isar database: $e");
    isarInitialized = false;
  }

  runApp(CattleManagerApp(
    isarInitialized: isarInitialized,
  ));
}

class CattleManagerApp extends StatefulWidget {
  final bool isarInitialized;

  const CattleManagerApp({
    super.key,
    required this.isarInitialized,
  });

  @override
  State<CattleManagerApp> createState() => _CattleManagerAppState();
}

class _CattleManagerAppState extends State<CattleManagerApp> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Cattle Manager',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
        useMaterial3: true,
        scaffoldBackgroundColor: AppTheme.scaffoldBackground,
        appBarTheme: AppTheme.appBarTheme,
        cardTheme: CardTheme(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: AppTheme.cardRadius),
        ),
        // Fix form field label cutting issue with Material 3
        inputDecorationTheme: const InputDecorationTheme(
          // Standard padding for non-floating labels
          contentPadding: EdgeInsets.fromLTRB(12, 16, 12, 16),
          floatingLabelBehavior: FloatingLabelBehavior.never,
          // Ensure labels are not dense to prevent truncation
          isDense: false,
          // Standard height for non-floating labels
          constraints: BoxConstraints(minHeight: 56),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
        ),
      ),
      home: widget.isarInitialized 
          ? const DashboardScreen()
          : const DatabaseErrorScreen(),
      routes: {
        AppRoutes.cattle: (context) => const CattleScreen(),
        AppRoutes.breeding: (context) => const BreedingScreen(),
        AppRoutes.health: (context) => const HealthScreen(),
        AppRoutes.milk: (context) => const MilkScreen(),
        AppRoutes.transactions: (context) => const TransactionsScreen(),
        AppRoutes.settings: (context) => const FarmSetupScreen(),
        AppRoutes.transactionsReport: (context) =>
            const TransactionsReportScreen(),
        AppRoutes.milkReport: (context) => const MilkReportScreen(),
        AppRoutes.cattleReport: (context) => const CattleReportScreen(),
        AppRoutes.eventsReport: (context) => const EventsReportScreen(),
        AppRoutes.breedingReport: (context) => const BreedingReportScreen(),
        AppRoutes.pregnanciesReport: (context) =>
            const PregnanciesReportScreen(),
        AppRoutes.weightReport: (context) => const WeightReportScreen(),
        AppRoutes.healthReport: (context) => const HealthReportScreen(),
      },
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en', ''),
      ],
    );
  }

  @override
  void dispose() {
    // Clean up Isar when the app is closed
    IsarInitializer.dispose();
    super.dispose();
  }
}

class DatabaseErrorScreen extends StatelessWidget {
  const DatabaseErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              const Text(
                'Database Error',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'The database failed to initialize. Please restart the app or contact support.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // You could add a retry mechanism here if needed
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const CattleManagerApp(
                        isarInitialized: false,
                      ),
                    ),
                  );
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
