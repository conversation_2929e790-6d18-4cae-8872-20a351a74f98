import 'package:flutter/material.dart';
import '../../widgets/dialogs/index.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../../../utils/message_utils.dart';

/// Ultra-clean facade using centralized ModuleFormConfigs
class HealthRecordFormDialog {
  static Future<HealthRecordIsar?> show({
    required BuildContext context,
    HealthRecordIsar? healthRecord,
    required List<CattleIsar> cattle,
    Future<void> Function(HealthRecordIsar)? onSave,
  }) async {
    const formConfigs = ModuleFormConfigs();

    return await UniversalFormDialog.show<HealthRecordIsar>(
      context: context,
      config: FormConfig.simple(
        title: healthRecord == null ? 'Add Health Record' : 'Edit Health Record',
        fields: formConfigs.forHealthRecord(),
      ),
      initialValues: healthRecord != null ? {
        FormKeys.cattleId.value: healthRecord.cattleId,
        FormKeys.date.value: healthRecord.date ?? DateTime.now(),
        FormKeys.condition.value: healthRecord.diagnosis ?? '',
        FormKeys.treatment.value: healthRecord.treatment ?? '',
        FormKeys.veterinarian.value: healthRecord.veterinarian ?? '',
        FormKeys.cost.value: healthRecord.cost ?? 0.0,
        FormKeys.notes.value: healthRecord.notes ?? '',
      } : {
        FormKeys.date.value: DateTime.now(),
        FormKeys.cost.value: 0.0,
      },
      section: 'health',
      closeOnSave: true,
      onSave: (values) async {
        try {
          final record = HealthRecordIsar.create(
            recordId: healthRecord?.recordId,
            cattleId: values[FormKeys.cattleId.value] ?? '',
            date: values[FormKeys.date.value] ?? DateTime.now(),
            diagnosis: values[FormKeys.condition.value] ?? '',
            treatment: values[FormKeys.treatment.value] ?? '',
            notes: values[FormKeys.notes.value] ?? '',
            cost: values[FormKeys.cost.value] ?? 0.0,
            veterinarian: values[FormKeys.veterinarian.value] ?? '',
          );
          if (onSave != null) await onSave(record);
          return record;
        } catch (e) {
          HealthMessageUtils.showError(context, 'Failed to save health record.');
          return false;
        }
      },
    );
  }
}
