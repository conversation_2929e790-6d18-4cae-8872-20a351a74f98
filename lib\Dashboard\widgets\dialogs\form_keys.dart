/// Enum defining all possible form field keys across the entire application
/// This provides compile-time safety and prevents typos in field references
///
/// Uses Dar<PERSON>'s enhanced enum features to eliminate boilerplate:
/// - Access string value: FormKeys.cattle.name
/// - Parse from string: FormKeys.values.byName('cattle')
/// - Safe parsing: FormKeys.tryParse('cattle')
enum FormKeys {
  //__ Weight Form Fields __//
  cattle,
  weight,
  weightUnit,
  measurementDate,
  measurementMethod,
  measurementLocation,
  measurementQuality,
  healthStatus,
  feedingStatus,
  bodyConditionScore,
  isPregnant,
  pregnancyStage,
  season,
  feedQuality,
  isEstimate,
  confidenceLevel,

  //__ Cattle Form Fields __//
  tagId,
  autoGenerateTagId,
  name,
  animalType,
  gender,
  breed,
  source,
  dateOfBirth,
  purchaseDate,
  purchasePrice,
  color,
  notes,
  motherTagId,

  //__ Health Record Form Fields __//
  cattleId,
  date,
  condition,
  diagnosis,
  treatment,
  veterinarian,
  cost,

  //__ Breeding Form Fields __//
  breedingDate,
  breedingMethod,
  bullId,
  expectedDeliveryDate,
  expectedDate, // Alternative name for expectedDeliveryDate
  breedingNotes,

  //__ Pregnancy Form Fields __//
  startDate,
  status,
  deliveryDate,
  calvingDifficulty,
  pregnancyNotes,

  //__ Delivery Form Fields __//
  deliveryMethod,
  assistanceRequired,
  calfCount,
  calfDetails,

  //__ Milk Record Form Fields __//
  morningAmount,
  eveningAmount,
  totalAmount,
  milkQuality,
  milkNotes,

  //__ Transaction Form Fields __//
  amount,
  category,
  categoryId, // Category ID reference
  categoryType,
  description,
  paymentMethod,

  //__ Vaccination Form Fields __//
  vaccineName,
  vaccineType,
  dosage,
  administeredBy,
  nextDueDate,
  vaccinationNotes,

  //__ Treatment Form Fields __//
  treatmentType,
  medication,
  dosageAmount,
  frequency,
  duration,
  treatmentNotes,

  //__ Common Fields (Used Across Multiple Forms) __//
  location,
  quality,
  method,
  type,
  title, // Event/record title
  priority, // Priority level
  unit, // Unit of measurement
  quantity, // Quantity amount
  price, // Price/cost
  buyer, // Buyer name
  paymentStatus, // Payment status
  remarks,
  createdDate,
  modifiedDate,
  createdBy,
  modifiedBy;

  /// Get the string representation of the form key
  /// Uses the built-in enum name property - no boilerplate needed!
  String get value => this.name;

  /// Parse FormKeys from string value with error handling
  /// Returns null if no match is found (safe parsing)
  static FormKeys? tryParse(String? value) {
    if (value == null) return null;

    try {
      return FormKeys.values.byName(value);
    } catch (e) {
      return null; // Gracefully handle if no match is found
    }
  }

  /// Legacy method for backward compatibility
  /// @deprecated Use tryParse instead for better error handling
  @Deprecated('Use tryParse instead')
  static FormKeys? fromString(String value) => tryParse(value);

  /// Get all form keys as strings (useful for debugging/logging)
  static List<String> get allKeys => values.map((key) => key.name).toList();

  /// Check if a string is a valid form key
  static bool isValidKey(String? value) => tryParse(value) != null;
}


