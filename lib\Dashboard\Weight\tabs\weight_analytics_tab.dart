import 'package:flutter/material.dart';
import '../controllers/weight_controller.dart';
import '../../widgets/index.dart';

class WeightAnalyticsTab extends StatefulWidget {
  final WeightController controller;

  const WeightAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<WeightAnalyticsTab> createState() => _WeightAnalyticsTabState();
}

class _WeightAnalyticsTabState extends State<WeightAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        if (!widget.controller.hasData) {
          return EmptyState.custom(
            icon: Icons.analytics,
            title: 'No Weight Data',
            message: 'Add weight records to see analytics and insights.',
          );
        }

        final analyticsSummary = widget.controller.analyticsSummary;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DateRangeFilterWidget(
                startDate: widget.controller.startDate,
                endDate: widget.controller.endDate,
                theme: DateRangeTheme.weight,
                onRangeChanged: (start, end) {
                  widget.controller.setDateRange(start, end);
                },
                compact: true,
              ),
          const SizedBox(height: 16),
          _buildSummaryCards(analyticsSummary),
          const SizedBox(height: 16),
          _buildWeightTrendsCard(),
          const SizedBox(height: 16),
          _buildCattleDistributionCard(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCards(analyticsSummary) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Total Records',
            analyticsSummary.totalRecords.toString(),
            Icons.list_alt,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Cattle Count',
            analyticsSummary.totalCattle.toString(),
            Icons.pets,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Avg Weight',
            '${analyticsSummary.averageWeight.toStringAsFixed(1)} kg',
            Icons.monitor_weight,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeightTrendsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'Weight Trends',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              child: const Center(
                child: Text(
                  'Weight trend chart will be displayed here',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCattleDistributionCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: Colors.purple),
                const SizedBox(width: 8),
                const Text(
                  'Weight Distribution',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              child: const Center(
                child: Text(
                  'Weight distribution chart will be displayed here',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}