import 'package:flutter/material.dart';
import '../models/milk_sale_isar.dart';
import '../services/milk_sales_service.dart';
import '../../widgets/dialogs/index.dart';

/// Ultra-clean facade using centralized ModuleFormConfigs
class MilkSaleEntryDialog {
  static Future<MilkSaleIsar?> show({
    required BuildContext context,
    required DateTime selectedDate,
    required double availableMilk,
  }) async {
    const formConfigs = ModuleFormConfigs();

    return await UniversalFormDialog.show<MilkSaleIsar>(
      context: context,
      config: FormConfig.simple(
        title: 'Milk Sale Entry',
        fields: formConfigs.forMilkSaleEntry(availableMilk),
      ),
      initialValues: {
        FormKeys.unit.value: 'Liters',
        FormKeys.paymentMethod.value: 'Cash',
        FormKeys.paymentStatus.value: 'Paid',
      },
      section: 'milk_sales',
      closeOnSave: true,
      onSave: (values) async {
        try {
          final sale = MilkSaleIsar()
            ..date = selectedDate
            ..quantity = values[FormKeys.quantity.value] ?? 0.0
            ..price = values[FormKeys.price.value] ?? 0.0
            ..total = (values[FormKeys.quantity.value] ?? 0.0) * (values[FormKeys.price.value] ?? 0.0)
            ..buyer = values[FormKeys.buyer.value] ?? ''
            ..paymentMethod = values[FormKeys.paymentMethod.value] ?? 'Cash'
            ..paymentStatus = values[FormKeys.paymentStatus.value] ?? 'Paid'
            ..notes = values[FormKeys.notes.value] ?? ''
            ..createdAt = DateTime.now()
            ..updatedAt = DateTime.now();
          await MilkSalesService().addMilkSale(sale);
          return sale;
        } catch (e) {
          return false;
        }
      },
    );
  }


}
