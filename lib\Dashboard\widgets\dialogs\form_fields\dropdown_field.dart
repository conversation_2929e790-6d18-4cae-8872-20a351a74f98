import 'package:flutter/material.dart';
import '../form_config.dart';
import '../universal_form_controller.dart';
import '../config/ui_theme_service.dart';
import '../dropdown_option.dart';

/// Universal dropdown field widget for the form system
class UniversalDropdownField extends StatefulWidget {
  final UniversalFormController controller;
  final FormFieldConfig config;
  final Color themeColor;

  const UniversalDropdownField({
    Key? key,
    required this.controller,
    required this.config,
    required this.themeColor,
  }) : super(key: key);

  @override
  State<UniversalDropdownField> createState() => _UniversalDropdownFieldState();
}

class _UniversalDropdownFieldState extends State<UniversalDropdownField> {
  List<DropdownOption> _options = [];
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _previousDependencyValues = {};

  /// Getter for cleaner field key access
  String get fieldKey => widget.config.key.value;

  @override
  void initState() {
    super.initState();
    _initializeDependencyValues();

    // Handle static vs dynamic options
    if (widget.config.options != null) {
      // Static options - set once
      _options = widget.config.options!;
      _isLoading = false;
      _error = null;
    } else if (widget.config.optionsLoader != null) {
      // Dynamic options - add listener and defer initial load
      widget.controller.addListener(_handleControllerChange);

      // Defer initial load until after build is complete
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _loadOptions();
        }
      });
    }
  }

  @override
  void didUpdateWidget(UniversalDropdownField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the controller instance itself has changed...
    if (widget.controller != oldWidget.controller) {
      // Remove listener from old controller (if it had dynamic options)
      if (oldWidget.config.optionsLoader != null) {
        oldWidget.controller.removeListener(_handleControllerChange);
      }

      // Add listener to new controller (if it has dynamic options)
      if (widget.config.optionsLoader != null) {
        widget.controller.addListener(_handleControllerChange);
      }

      // Also reinitialize dependency values for the new controller
      _initializeDependencyValues();
    }
  }

  @override
  void dispose() {
    // Only remove listener if it was added (for dynamic options)
    if (widget.config.optionsLoader != null) {
      widget.controller.removeListener(_handleControllerChange);
    }
    super.dispose();
  }

  /// Initialize dependency values tracking
  void _initializeDependencyValues() {
    if (widget.config.dependencies != null) {
      for (final dependency in widget.config.dependencies!) {
        final parentKey = dependency.parentFieldKey.value;
        _previousDependencyValues[parentKey] = widget.controller.getValue(parentKey);
      }
    }
  }

  /// Handle controller changes (for dependencies)
  void _handleControllerChange() {
    if (widget.config.dependencies == null) return;

    bool shouldReload = false;

    for (final dependency in widget.config.dependencies!) {
      final parentKey = dependency.parentFieldKey.value;
      final currentParentValue = widget.controller.getValue(parentKey);
      final previousParentValue = _previousDependencyValues[parentKey];

      // Only reload if the parent value actually changed
      if (currentParentValue != previousParentValue) {
        _previousDependencyValues[parentKey] = currentParentValue;

        // Check if this change is relevant to our dependency
        final formValues = widget.controller.getAllValues();
        if (dependency.evaluate?.call(formValues) ??
            currentParentValue == dependency.parentValue) {
          shouldReload = true;
          break;
        }
      }
    }

    if (shouldReload) {
      _loadOptions();
    }
  }

  /// Load dropdown options (for dynamic options only)
  /// This method's ONLY job is to orchestrate the dynamic load via the controller
  Future<void> _loadOptions() async {
    if (widget.config.optionsLoader == null) return;

    // Set initial loading state
    setState(() {
      _isLoading = widget.controller.isLoadingOptions(fieldKey);
      _error = widget.controller.getOptionsError(fieldKey);
    });

    // Check if options are already cached
    final cachedOptions = widget.controller.getOptions(fieldKey);
    if (cachedOptions != null) {
      setState(() {
        _options = cachedOptions.cast<DropdownOption>();
        _isLoading = false;
        _error = null;
      });
      return;
    }

    // Load options through controller (controller handles caching, loading state, and errors)
    await widget.controller.loadOptionsForField(
      fieldKey,
      () async {
        final options = await widget.config.optionsLoader!();
        return options.cast<dynamic>();
      },
    );

    // After loading, get the final state from the controller
    if (mounted) {
      setState(() {
        _options = widget.controller.getOptions(fieldKey)?.cast<DropdownOption>() ?? [];
        _isLoading = widget.controller.isLoadingOptions(fieldKey);
        _error = widget.controller.getOptionsError(fieldKey);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final uiTheme = UiThemeService();

    if (_isLoading) {
      return _buildLoadingField(uiTheme);
    }

    if (_error != null) {
      return _buildErrorField(uiTheme);
    }

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        final currentValue = widget.controller.getValue(fieldKey);
        final errorText = widget.controller.validationErrors[fieldKey];



        return DropdownButtonFormField<String>(
          value: _getValidValue(currentValue),
          items: _options.map((option) => _buildDropdownItem(option)).toList(),
          onChanged: widget.config.enabled ? _handleValueChange : null,
          decoration: uiTheme.getFormFieldDecoration(
            label: widget.config.label,
            hint: widget.config.hint,
            icon: widget.config.icon,
            themeColor: widget.themeColor,
            errorText: errorText,
            prefix: widget.config.prefix,
            suffix: widget.config.suffix,
            isRequired: widget.config.validationRules.any((rule) => rule.type == ValidationRuleType.required),
          ),
          validator: (value) => errorText,
          isExpanded: true,
          icon: Icon(
            Icons.arrow_drop_down,
            color: widget.themeColor,
          ),
          style: TextStyle(
            color: Theme.of(context).textTheme.bodyLarge?.color,
            fontSize: 14, // Reduced from 16 to 14 to help with label clipping
          ),
          dropdownColor: Theme.of(context).cardColor,
        );
      },
    );
  }

  /// Build dropdown menu item
  DropdownMenuItem<String> _buildDropdownItem(DropdownOption option) {
    return DropdownMenuItem<String>(
      value: option.value,
      enabled: option.enabled,
      child: Row(
        children: [
          if (option.icon != null) ...[
            Icon(
              option.icon,
              color: option.color ?? widget.themeColor,
              size: 20,
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              option.label,
              style: TextStyle(
                color: option.enabled 
                    ? (option.color ?? Theme.of(context).textTheme.bodyLarge?.color)
                    : Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading state field
  Widget _buildLoadingField(UiThemeService uiTheme) {
    return InputDecorator(
      decoration: uiTheme.getFormFieldDecoration(
        label: widget.config.label,
        hint: 'Loading options...',
        icon: widget.config.icon,
        themeColor: widget.themeColor,
        isRequired: widget.config.validationRules.any((rule) => rule.type == ValidationRuleType.required),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(widget.themeColor),
            ),
          ),
          const SizedBox(width: 8),
          const Text('Loading options...'),
        ],
      ),
    );
  }

  /// Build error state field
  Widget _buildErrorField(UiThemeService uiTheme) {
    return InputDecorator(
      decoration: uiTheme.getFormFieldDecoration(
        label: widget.config.label,
        hint: 'Error loading options',
        icon: widget.config.icon,
        themeColor: Colors.red,
        errorText: _error,
        isRequired: widget.config.validationRules.any((rule) => rule.type == ValidationRuleType.required),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Colors.red, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Error loading options',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          TextButton(
            onPressed: _loadOptions,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Get valid value (ensure it exists in options)
  String? _getValidValue(dynamic value) {
    if (value == null) return null;

    final stringValue = value.toString();

    // If still loading, trust the initial value from controller
    if (_isLoading) {
      return stringValue;
    }

    final hasOption = _options.any((option) => option.value == stringValue);

    // If options are loaded but value is invalid, clear it in controller
    if (!hasOption && !_isLoading) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.controller.setValue(fieldKey, null);
      });
      return null;
    }

    return hasOption ? stringValue : null;
  }

  /// Handle value changes
  void _handleValueChange(String? value) {
    widget.controller.setValue(fieldKey, value);
  }
}
