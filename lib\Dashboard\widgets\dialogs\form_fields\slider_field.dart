import 'package:flutter/material.dart';
import '../form_config.dart';
import '../universal_form_controller.dart';
import '../config/ui_theme_service.dart';
import '../form_field_utils.dart';

/// Universal slider field widget for the form system
class UniversalSliderField extends StatefulWidget {
  final UniversalFormController controller;
  final FormFieldConfig config;
  final Color themeColor;

  const UniversalSliderField({
    Key? key,
    required this.controller,
    required this.config,
    required this.themeColor,
  }) : super(key: key);

  @override
  State<UniversalSliderField> createState() => _UniversalSliderFieldState();
}

class _UniversalSliderFieldState extends State<UniversalSliderField> {
  late double _localValue;
  bool _isDragging = false;

  /// Getter for cleaner field key access
  String get fieldKey => widget.config.key.value;

  @override
  void initState() {
    super.initState();
    _localValue = _getCurrentValue();
  }

  @override
  void didUpdateWidget(UniversalSliderField oldWidget) {
    super.didUpdateWidget(oldWidget);

    final currentValue = _getCurrentValue();

    // Only update the local state if:
    // 1. The widget is not currently being dragged by the user.
    // 2. The controller's value is different from our local display value.
    // This prevents an unnecessary setState call if the values are already in sync.
    if (!_isDragging && currentValue != _localValue) {
      setState(() {
        _localValue = currentValue; // Re-sync with controller's source of truth
      });
    }
  }

  /// Get current value from controller with proper type handling
  double _getCurrentValue() {
    final value = widget.controller.getValue(fieldKey);
    if (value is num) {
      return value.toDouble();
    }
    return widget.config.initialValue as double? ?? 0.0;
  }

  /// Handle value changes during dragging
  void _handleValueChange(double value) {
    setState(() {
      _localValue = value;
      _isDragging = true;
    });
  }

  /// Handle value changes when dragging ends
  void _handleValueChangeEnd(double value) {
    setState(() {
      _localValue = value;
      _isDragging = false;
    });
    widget.controller.setValue(fieldKey, value);
  }

  @override
  Widget build(BuildContext context) {
    final minValue = widget.config.minValue ?? 0.0;
    final maxValue = widget.config.maxValue ?? 1.0;

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        final errorText = widget.controller.validationErrors[fieldKey];

        // Use local value during dragging, controller value otherwise
        final displayValue = _isDragging ? _localValue : _getCurrentValue();
        if (!_isDragging) {
          _localValue = displayValue;
        }

        return FormField<double>(
          initialValue: displayValue,
          validator: (value) => errorText,
          builder: (FormFieldState<double> field) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Label and current value
                Row(
                  children: [
                    if (widget.config.icon != null) ...[
                      Icon(
                        widget.config.icon,
                        color: widget.themeColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                    ],
                    Expanded(
                      child: Text(
                        widget.config.label,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: widget.themeColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: widget.themeColor.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        widget.config.formatValue(displayValue),
                        style: TextStyle(
                          color: widget.themeColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Slider
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: errorText != null
                          ? Colors.red
                          : widget.themeColor.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          activeTrackColor: widget.themeColor,
                          inactiveTrackColor: widget.themeColor.withValues(alpha: 0.3),
                          thumbColor: widget.themeColor,
                          overlayColor: widget.themeColor.withValues(alpha: 0.2),
                          valueIndicatorColor: widget.themeColor,
                          valueIndicatorTextStyle: TextStyle(
                            color: UiThemeService().getContrastingTextColor(widget.themeColor),
                            fontWeight: FontWeight.w600,
                          ),
                          trackHeight: 4,
                          thumbShape: const RoundSliderThumbShape(
                            enabledThumbRadius: 10,
                          ),
                          overlayShape: const RoundSliderOverlayShape(
                            overlayRadius: 20,
                          ),
                        ),
                        child: Slider(
                          value: widget.config.clampValue(displayValue),
                          min: minValue,
                          max: maxValue,
                          divisions: widget.config.sliderDivisions,
                          label: widget.config.formatValue(displayValue),
                          onChanged: widget.config.enabled ? _handleValueChange : null,
                          onChangeEnd: widget.config.enabled ? _handleValueChangeEnd : null,
                        ),
                      ),
                      
                      // Min/Max labels
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              widget.config.formatValue(minValue),
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              widget.config.formatValue(maxValue),
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                    ],
                  ),
                ),
                
                // Error text
                if (errorText != null) ...[
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: Text(
                      errorText,
                      style: TextStyle(
                        color: Colors.red[700],
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
                
                // Hint text
                if (widget.config.hint != null) ...[
                  const SizedBox(height: 4),
                  Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: Text(
                      widget.config.hint!,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ],
            );
          },
        );
      },
    );
  }
}
