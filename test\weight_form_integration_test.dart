import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:cattle_manager/Dashboard/Weight/dialogs/weight_form_dialog.dart';
import 'package:cattle_manager/Dashboard/Cattle/models/cattle_isar.dart';

void main() {
  group('WeightFormDialog Integration Tests', () {
    late List<CattleIsar> testCattle;

    setUp(() {
      // Create test cattle data
      testCattle = [
        CattleIsar()
          ..businessId = 'cattle-1'
          ..name = '<PERSON><PERSON>'
          ..tagId = 'C001',
        CattleIsar()
          ..businessId = 'cattle-2'
          ..name = 'Daisy'
          ..tagId = 'C002',
      ];
    });

    testWidgets('WeightFormDialog.show displays correctly', (WidgetTester tester) async {
      bool dialogShown = false;

      // Build a test app with the dialog
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () async {
                  dialogShown = true;
                  await WeightFormDialog.show(
                    context: context,
                    cattle: testCattle,
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // Tap the button to show the dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify the dialog was attempted to be shown
      expect(dialogShown, isTrue);
    });

    test('WeightFormDialog class exists and has show method', () {
      // Verify the class structure
      expect(WeightFormDialog, isNotNull);
      
      // This test verifies that the refactoring maintained the public API
      // The show method should be accessible as a static method
      expect(WeightFormDialog.show, isNotNull);
    });

    test('Test cattle data structure', () {
      expect(testCattle.length, equals(2));
      expect(testCattle.first.businessId, equals('cattle-1'));
      expect(testCattle.first.name, equals('Bessie'));
      expect(testCattle.first.tagId, equals('C001'));
    });
  });
}
