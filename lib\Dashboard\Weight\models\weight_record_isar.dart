import 'package:isar/isar.dart';

part 'weight_record_isar.g.dart';

@collection
class WeightRecordIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  String? cattleBusinessId;

  @Index()
  String? farmBusinessId;

  // Core weight measurement data
  double weight = 0.0;
  DateTime? measurementDate;
  String? measurementMethod; // 'scale', 'tape', 'visual_estimate'
  String? measurementQuality; // 'excellent', 'good', 'fair', 'poor'
  String? measurementLocation;
  bool? isEstimate;
  double? confidenceLevel;

  // Body condition scoring
  double? bodyConditionScore;
  String? bodyConditionNotes;

  // Weight tracking and analysis
  double? previousWeight;
  double? weightGain;
  double? dailyGain;
  int? daysSinceLastMeasurement;

  // Weight goals and targets
  String? weightGoal; // 'gain', 'maintain', 'lose'
  double? targetWeight;
  DateTime? targetDate;

  // Animal status information
  String? healthStatus;
  String? feedingStatus;
  String? feedQuality;
  bool? isPregnant;
  int? pregnancyStage;
  String? season;
  String? weatherConditions;

  // Validation and quality control
  bool? isValidated;
  DateTime? validatedAt;
  String? validatedBy;

  // Measurement context
  String? measuredBy;
  String? notes;
  String? weightUnit; // 'kg', 'lbs'

  // Audit fields
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;

  WeightRecordIsar();

  /// Factory constructor to create WeightRecordIsar from form values
  /// Used by the universal form system to map form data to model
  factory WeightRecordIsar.fromFormValues(Map<String, dynamic> values) {
    return WeightRecordIsar()
      ..cattleBusinessId = values['cattle'] as String?
      ..weight = (values['weight'] as num?)?.toDouble() ?? 0.0
      ..weightUnit = values['weightUnit'] as String?
      ..measurementDate = values['measurementDate'] as DateTime?
      ..measurementMethod = values['measurementMethod'] as String?
      ..measurementLocation = values['measurementLocation'] as String?
      ..measurementQuality = values['measurementQuality'] as String?
      ..healthStatus = values['healthStatus'] as String?
      ..feedingStatus = values['feedingStatus'] as String?
      ..bodyConditionScore = (values['bodyConditionScore'] as num?)?.toDouble()
      ..isPregnant = values['isPregnant'] as bool?
      ..pregnancyStage = values['pregnancyStage'] as int?
      ..season = values['season'] as String?
      ..feedQuality = values['feedQuality'] as String?
      ..isEstimate = values['isEstimate'] as bool?
      ..confidenceLevel = (values['confidenceLevel'] as num?)?.toDouble()
      ..measuredBy = values['measuredBy'] as String?
      ..notes = values['notes'] as String?
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();
  }

  // Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'businessId': businessId,
      'cattleBusinessId': cattleBusinessId,
      'farmBusinessId': farmBusinessId,
      'weight': weight,
      'measurementDate': measurementDate?.toIso8601String(),
      'measurementMethod': measurementMethod,
      'measurementQuality': measurementQuality,
      'measurementLocation': measurementLocation,
      'isEstimate': isEstimate,
      'confidenceLevel': confidenceLevel,
      'bodyConditionScore': bodyConditionScore,
      'bodyConditionNotes': bodyConditionNotes,
      'previousWeight': previousWeight,
      'weightGain': weightGain,
      'dailyGain': dailyGain,
      'daysSinceLastMeasurement': daysSinceLastMeasurement,
      'weightGoal': weightGoal,
      'targetWeight': targetWeight,
      'targetDate': targetDate?.toIso8601String(),
      'healthStatus': healthStatus,
      'feedingStatus': feedingStatus,
      'feedQuality': feedQuality,
      'isPregnant': isPregnant,
      'pregnancyStage': pregnancyStage,
      'season': season,
      'weatherConditions': weatherConditions,
      'isValidated': isValidated,
      'validatedAt': validatedAt?.toIso8601String(),
      'validatedBy': validatedBy,
      'measuredBy': measuredBy,
      'notes': notes,
      'weightUnit': weightUnit,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
    };
  }

  @override
  String toString() {
    return 'WeightRecordIsar{id: $id, businessId: $businessId, cattleBusinessId: $cattleBusinessId, weight: $weight, measurementDate: $measurementDate}';
  }
}

@collection
class WeightGoalIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  String? cattleBusinessId;

  @Index()
  String? farmBusinessId;

  // Goal details
  String? goalType; // 'weight_gain', 'weight_loss', 'weight_maintenance'
  double? targetWeight;
  double? startingWeight;
  double? currentWeight;
  DateTime? startDate;
  DateTime? targetDate;
  DateTime? achievedDate;

  // Progress tracking
  double? weightToGain;
  double? weightToLose;
  double? dailyTargetGain;
  double? weeklyTargetGain;
  double? progressPercentage;
  bool? isAchieved;
  String? status; // 'active', 'achieved', 'paused', 'cancelled'

  // Additional information
  String? notes;
  DateTime? createdAt;
  DateTime? updatedAt;

  WeightGoalIsar();
}