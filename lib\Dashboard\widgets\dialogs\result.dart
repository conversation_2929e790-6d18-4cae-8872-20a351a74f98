/// A sealed class representing the result of an operation that can either succeed or fail
/// This provides type-safe error handling without exceptions
sealed class Result<T, E> {
  const Result();

  /// Create a successful result
  const factory Result.success(T value) = Success<T, E>;

  /// Create a failed result
  const factory Result.failure(E error) = Failure<T, E>;

  /// Check if this result is a success
  bool get isSuccess => this is Success<T, E>;

  /// Check if this result is a failure
  bool get isFailure => this is Failure<T, E>;

  /// Get the success value, or null if this is a failure
  T? get valueOrNull => switch (this) {
    Success<T, E>(value: final value) => value,
    Failure<T, E>() => null,
  };

  /// Get the error, or null if this is a success
  E? get errorOrNull => switch (this) {
    Success<T, E>() => null,
    Failure<T, E>(error: final error) => error,
  };

  /// Transform the success value using the provided function
  Result<R, E> map<R>(R Function(T) transform) => switch (this) {
    Success<T, E>(value: final value) => Result.success(transform(value)),
    Failure<T, E>(error: final error) => Result.failure(error),
  };

  /// Transform the error using the provided function
  Result<T, R> mapError<R>(R Function(E) transform) => switch (this) {
    Success<T, E>(value: final value) => Result.success(value),
    Failure<T, E>(error: final error) => Result.failure(transform(error)),
  };

  /// Execute different functions based on whether this is a success or failure
  R when<R>({
    required R Function(T value) success,
    required R Function(E error) failure,
  }) => switch (this) {
    Success<T, E>(value: final value) => success(value),
    Failure<T, E>(error: final error) => failure(error),
  };

  /// Execute different functions based on whether this is a success or failure (async version)
  Future<R> whenAsync<R>({
    required Future<R> Function(T value) success,
    required Future<R> Function(E error) failure,
  }) => switch (this) {
    Success<T, E>(value: final value) => success(value),
    Failure<T, E>(error: final error) => failure(error),
  };

  /// Get the success value or throw the error
  T unwrap() => switch (this) {
    Success<T, E>(value: final value) => value,
    Failure<T, E>(error: final error) => throw Exception(error.toString()),
  };

  /// Get the success value or return the provided default
  T unwrapOr(T defaultValue) => switch (this) {
    Success<T, E>(value: final value) => value,
    Failure<T, E>() => defaultValue,
  };

  /// Get the success value or compute it using the provided function
  T unwrapOrElse(T Function(E error) defaultValue) => switch (this) {
    Success<T, E>(value: final value) => value,
    Failure<T, E>(error: final error) => defaultValue(error),
  };
}

/// Represents a successful result
final class Success<T, E> extends Result<T, E> {
  final T value;
  
  const Success(this.value);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Success<T, E> && value == other.value;

  @override
  int get hashCode => value.hashCode;

  @override
  String toString() => 'Success($value)';
}

/// Represents a failed result
final class Failure<T, E> extends Result<T, E> {
  final E error;
  
  const Failure(this.error);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Failure<T, E> && error == other.error;

  @override
  int get hashCode => error.hashCode;

  @override
  String toString() => 'Failure($error)';
}

/// Service error types for form data operations
enum ServiceErrorType {
  networkError,
  databaseError,
  validationError,
  notFound,
  unauthorized,
  timeout,
  unknown,
}

/// Represents an error that can occur in service operations
class ServiceError {
  final ServiceErrorType type;
  final String message;
  final String? details;
  final Object? originalError;

  const ServiceError({
    required this.type,
    required this.message,
    this.details,
    this.originalError,
  });

  /// Create a network error
  factory ServiceError.networkError(String message, [Object? originalError]) =>
      ServiceError(
        type: ServiceErrorType.networkError,
        message: message,
        originalError: originalError,
      );

  /// Create a database error
  factory ServiceError.databaseError(String message, [Object? originalError]) =>
      ServiceError(
        type: ServiceErrorType.databaseError,
        message: message,
        originalError: originalError,
      );

  /// Create a validation error
  factory ServiceError.validationError(String message, [String? details]) =>
      ServiceError(
        type: ServiceErrorType.validationError,
        message: message,
        details: details,
      );

  /// Create a not found error
  factory ServiceError.notFound(String message) =>
      ServiceError(
        type: ServiceErrorType.notFound,
        message: message,
      );

  /// Create an unauthorized error
  factory ServiceError.unauthorized(String message) =>
      ServiceError(
        type: ServiceErrorType.unauthorized,
        message: message,
      );

  /// Create a timeout error
  factory ServiceError.timeout(String message) =>
      ServiceError(
        type: ServiceErrorType.timeout,
        message: message,
      );

  /// Create an unknown error
  factory ServiceError.unknown(String message, [Object? originalError]) =>
      ServiceError(
        type: ServiceErrorType.unknown,
        message: message,
        originalError: originalError,
      );

  /// Get a user-friendly error message
  String get userMessage => switch (type) {
    ServiceErrorType.networkError => 'Network connection error. Please check your internet connection.',
    ServiceErrorType.databaseError => 'Database error. Please try again.',
    ServiceErrorType.validationError => message,
    ServiceErrorType.notFound => 'The requested data was not found.',
    ServiceErrorType.unauthorized => 'You are not authorized to perform this action.',
    ServiceErrorType.timeout => 'The operation timed out. Please try again.',
    ServiceErrorType.unknown => 'An unexpected error occurred. Please try again.',
  };

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceError &&
          type == other.type &&
          message == other.message &&
          details == other.details;

  @override
  int get hashCode => Object.hash(type, message, details);

  @override
  String toString() => 'ServiceError(type: $type, message: $message, details: $details)';
}

/// Type alias for common service result types
typedef ServiceResult<T> = Result<T, ServiceError>;
