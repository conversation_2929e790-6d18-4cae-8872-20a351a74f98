import 'package:flutter/material.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';

/// A widget that displays a single transaction item in a list
class TransactionListItem extends StatelessWidget {
  final TransactionIsar transaction;
  final CategoryIsar category;
  final VoidCallback onDelete;
  final VoidCallback onEdit;

  const TransactionListItem({
    Key? key,
    required this.transaction,
    required this.category,
    required this.onDelete,
    required this.onEdit,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: transaction.categoryType == 'Income' ? Colors.green : Colors.red,
        child: Icon(
          transaction.categoryType == 'Income' ? Icons.arrow_upward : Icons.arrow_downward,
          color: Colors.white,
        ),
      ),
      title: Text(transaction.category),
      subtitle: Text(transaction.description),
      trailing: Text(
        '\$${transaction.amount.toStringAsFixed(2)}',
        style: TextStyle(
          color: transaction.categoryType == 'Income' ? Colors.green : Colors.red,
          fontWeight: FontWeight.bold,
        ),
      ),
      onTap: onEdit,
    );
  }
} 