import 'package:flutter/material.dart';
import '../models/event_isar.dart';
import '../../widgets/dialogs/index.dart';

/// Ultra-clean facade using centralized ModuleFormConfigs
class EventFormDialog {
  static Future<EventIsar?> show({
    required BuildContext context,
    required String cattleId,
    EventIsar? event,
  }) async {
    const formConfigs = ModuleFormConfigs();

    return await UniversalFormDialog.show<EventIsar>(
      context: context,
      config: FormConfig.simple(
        title: event == null ? 'Add Event' : 'Edit Event',
        fields: formConfigs.forEvent(),
      ),
      initialValues: event != null ? {
        FormKeys.title.value: event.title,
        FormKeys.type.value: event.type?.value,
        FormKeys.date.value: event.eventDate ?? DateTime.now(),
        FormKeys.priority.value: event.priority.name,
        FormKeys.notes.value: event.notes ?? '',
      } : {
        FormKeys.date.value: DateTime.now(),
        FormKeys.type.value: 'Health Check',
        FormKeys.priority.value: 'medium',
      },
      section: 'events',
      closeOnSave: true,
      onSave: (values) async {
        try {
          final eventRecord = event ?? EventIsar();

          // Update the event properties
          eventRecord.title = values[FormKeys.title.value] ?? '';
          eventRecord.type = EventTypeEmbedded()..value = values[FormKeys.type.value] ?? 'Health Check';
          eventRecord.eventDate = values[FormKeys.date.value] ?? DateTime.now();
          eventRecord.priority = EventPriority.values.firstWhere(
            (p) => p.name == values[FormKeys.priority.value],
            orElse: () => EventPriority.medium,
          );
          eventRecord.notes = values[FormKeys.notes.value] ?? '';
          eventRecord.cattleId = cattleId;
          eventRecord.updatedAt = DateTime.now();

          return eventRecord;
        } catch (e) {
          return false;
        }
      },
    );
  }


}
