import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/milk_record_isar.dart';
import '../services/milk_handler.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../widgets/dialogs/index.dart';

/// Ultra-clean facade using centralized ModuleFormConfigs
class MilkFormDialog {
  static Future<MilkRecordIsar?> show({
    required BuildContext context,
    required List<CattleIsar> cattle,
    MilkRecordIsar? record,
    String? cattleTagId,
  }) async {
    const formConfigs = ModuleFormConfigs();

    return await UniversalFormDialog.show<MilkRecordIsar>(
      context: context,
      config: FormConfig.simple(
        title: record == null ? 'Add Milk Record' : 'Edit Milk Record',
        fields: formConfigs.forMilkRecord(),
      ),
      initialValues: record != null ? {
        FormKeys.cattle.value: record.cattleTagId,
        FormKeys.date.value: record.date ?? DateTime.now(),
        FormKeys.morningAmount.value: record.morningAmount ?? 0.0,
        FormKeys.eveningAmount.value: record.eveningAmount ?? 0.0,
        FormKeys.notes.value: record.notes ?? '',
      } : {
        FormKeys.cattle.value: cattleTagId,
        FormKeys.date.value: DateTime.now(),
        FormKeys.morningAmount.value: 0.0,
        FormKeys.eveningAmount.value: 0.0,
      },
      section: 'milk',
      closeOnSave: true,
      onSave: (values) async {
        try {
          final milkRecord = record?.copyWith(
            cattleTagId: values[FormKeys.cattle.value],
            date: values[FormKeys.date.value],
            morningAmount: values[FormKeys.morningAmount.value],
            eveningAmount: values[FormKeys.eveningAmount.value],
            notes: values[FormKeys.notes.value],
          ) ?? MilkRecordIsar.create(
            cattleBusinessId: values[FormKeys.cattle.value] ?? '',
            cattleTagId: values[FormKeys.cattle.value] ?? '',
            date: values[FormKeys.date.value] ?? DateTime.now(),
            morningAmount: values[FormKeys.morningAmount.value] ?? 0.0,
            eveningAmount: values[FormKeys.eveningAmount.value] ?? 0.0,
            notes: values[FormKeys.notes.value] ?? '',
          );

          // Get the milk handler from dependency injection
          final milkHandler = GetIt.instance<MilkHandler>();
          await milkHandler.addMilkRecord(milkRecord);
          return milkRecord;
        } catch (e) {
          return false;
        }
      },
    );
  }


}
