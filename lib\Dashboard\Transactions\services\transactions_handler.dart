import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';

import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Consolidated handler for all Transactions module database operations
class TransactionsHandler {
  static final Logger _logger = Logger('TransactionsHandler');
  final IsarService _isarService;

  // Singleton instance
  static final TransactionsHandler _instance = TransactionsHandler._internal();
  static TransactionsHandler get instance => _instance;

  // Private constructor
  TransactionsHandler._internal()
      : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== TRANSACTIONS ===//

  /// Get all transactions
  Future<List<TransactionIsar>> getAllTransactions() async {
    try {
      return await _isar.transactionIsars.where().sortByDateDesc().findAll();
    } catch (e) {
      _logger.severe('Error getting all transactions: $e');
      throw DatabaseException('Failed to retrieve transactions', e.toString());
    }
  }

  /// Get transactions for a date range
  Future<List<TransactionIsar>> getTransactionsForDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      return await _isar.transactionIsars
          .filter()
          .dateBetween(startDate, endDate)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting transactions for date range: $e');
      throw DatabaseException('Failed to retrieve transactions', e.toString());
    }
  }

  /// Get transactions by category
  Future<List<TransactionIsar>> getTransactionsByCategory(
      String categoryId) async {
    try {
      if (categoryId.isEmpty) {
        throw ValidationException('Category ID is required');
      }

      return await _isar.transactionIsars
          .filter()
          .categoryEqualTo(categoryId)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting transactions by category $categoryId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve transactions', e.toString());
    }
  }

  /// Get transactions by type (income/expense)
  Future<List<TransactionIsar>> getTransactionsByType(String type) async {
    try {
      if (type.isEmpty) {
        throw ValidationException('Transaction type is required');
      }

      return await _isar.transactionIsars
          .filter()
          .categoryTypeEqualTo(type)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting transactions by type $type: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve transactions', e.toString());
    }
  }

  /// Add a new transaction
  Future<void> addTransaction(TransactionIsar transaction) async {
    try {
      await _validateTransaction(transaction, isNew: true);

      await _isar.writeTxn(() async {
        await _isar.transactionIsars.put(transaction);
      });

      _logger
          .info('Successfully added transaction: ${transaction.transactionId}');
    } catch (e) {
      _logger.severe('Error adding transaction: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add transaction', e.toString());
    }
  }

  /// Update an existing transaction
  Future<void> updateTransaction(TransactionIsar transaction) async {
    try {
      await _validateTransaction(transaction, isNew: false);

      await _isar.writeTxn(() async {
        await _isar.transactionIsars.put(transaction);
      });

      _logger.info(
          'Successfully updated transaction: ${transaction.transactionId}');
    } catch (e) {
      _logger.severe('Error updating transaction: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to update transaction', e.toString());
    }
  }

  /// Delete a transaction
  Future<void> deleteTransaction(String transactionId) async {
    try {
      if (transactionId.isEmpty) {
        throw ValidationException('Transaction ID is required');
      }

      await _isar.writeTxn(() async {
        final transaction = await _isar.transactionIsars
            .filter()
            .transactionIdEqualTo(transactionId)
            .findFirst();

        if (transaction == null) {
          throw RecordNotFoundException(
              'Transaction not found: $transactionId');
        }

        await _isar.transactionIsars.delete(transaction.id);
      });

      _logger.info('Successfully deleted transaction: $transactionId');
    } catch (e) {
      _logger.severe('Error deleting transaction: $transactionId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete transaction', e.toString());
    }
  }

  /// Get total income for a date range
  Future<double> getTotalIncomeForDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      final transactions = await _isar.transactionIsars
          .filter()
          .categoryTypeEqualTo('Income')
          .and()
          .dateBetween(startDate, endDate)
          .findAll();

      double total = 0.0;
      for (var transaction in transactions) {
        total += transaction.amount;
      }
      return total;
    } catch (e) {
      _logger.severe('Error calculating total income: $e');
      throw DatabaseException('Failed to calculate total income', e.toString());
    }
  }

  /// Get total expenses for a date range
  Future<double> getTotalExpensesForDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      final transactions = await _isar.transactionIsars
          .filter()
          .categoryTypeEqualTo('Expense')
          .and()
          .dateBetween(startDate, endDate)
          .findAll();

      double total = 0.0;
      for (var transaction in transactions) {
        total += transaction.amount;
      }
      return total;
    } catch (e) {
      _logger.severe('Error calculating total expenses: $e');
      throw DatabaseException(
          'Failed to calculate total expenses', e.toString());
    }
  }

  //=== TRANSACTION CATEGORIES ===//

  /// Get all transaction categories
  Future<List<CategoryIsar>> getAllCategories() async {
    try {
      return await _isar.categoryIsars.where().sortByName().findAll();
    } catch (e) {
      _logger.severe('Error getting all categories: $e');
      throw DatabaseException('Failed to retrieve categories', e.toString());
    }
  }

  /// Get all transaction categories (alias for getAllCategories)
  Future<List<CategoryIsar>> getAllTransactionCategories() async {
    return getAllCategories();
  }

  /// Validate transaction
  Future<void> _validateTransaction(TransactionIsar transaction,
      {required bool isNew}) async {
    if (transaction.amount <= 0) {
      throw ValidationException('Transaction amount must be greater than zero');
    }

    if (transaction.category.isEmpty) {
      throw ValidationException('Category is required');
    }

    if (transaction.categoryType.isEmpty) {
      throw ValidationException('Transaction type is required');
    }
  }
}
