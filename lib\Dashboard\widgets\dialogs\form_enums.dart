import 'dropdown_option.dart';

/// Type-safe enums for form data operations
/// Replaces string parameters with compile-time safe alternatives

/// Gender options for cattle
enum Gender {
  male('Male'),
  female('Female'),
  unknown('Unknown');

  const Gender(this.displayName);

  final String displayName;

  /// Parse gender from string value (case-insensitive)
  /// Uses stable enum name for parsing, not display name
  static Gender? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final gender in values) {
      if (gender.name.toLowerCase() == value.toLowerCase()) {
        return gender;
      }
    }

    // Support common abbreviations for convenience
    switch (value.toLowerCase()) {
      case 'm':
        return Gender.male;
      case 'f':
        return Gender.female;
      case 'u':
        return Gender.unknown;
      default:
        return null;
    }
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use tryParse instead')
  static Gender? fromString(String? value) => tryParse(value);

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all gender options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((gender) => DropdownOption(
      value: gender.value,
      label: gender.displayName,
    )).toList();
  }

  /// Get all display names
  static List<String> get displayNames => values.map((g) => g.displayName).toList();

  /// Get all values (enum names)
  static List<String> get allValues => values.map((g) => g.value).toList();
}

/// Transaction category types
enum TransactionCategoryType {
  income('Income'),
  expense('Expense');

  const TransactionCategoryType(this.displayName);

  final String displayName;

  /// Parse category type from string value (case-insensitive)
  /// Uses stable enum name for parsing, not display name
  static TransactionCategoryType? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final type in values) {
      if (type.name.toLowerCase() == value.toLowerCase()) {
        return type;
      }
    }
    return null;
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use tryParse instead')
  static TransactionCategoryType? fromString(String? value) => tryParse(value);

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all transaction category type options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((type) => DropdownOption(
      value: type.value,
      label: type.displayName,
    )).toList();
  }

  /// Get all display names
  static List<String> get displayNames => values.map((t) => t.displayName).toList();

  /// Get all values (enum names)
  static List<String> get allValues => values.map((t) => t.value).toList();
}

/// Cattle source options
enum CattleSource {
  bornAtFarm('Born at Farm'),
  purchased('Purchased'),
  inherited('Inherited'),
  gift('Gift'),
  other('Other');

  const CattleSource(this.displayName);

  final String displayName;

  /// Parse source from string value (case-insensitive)
  /// Uses stable enum name for parsing, not display name
  static CattleSource? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final source in values) {
      if (source.name.toLowerCase() == value.toLowerCase()) {
        return source;
      }
    }

    // Support legacy display name matching for backward compatibility
    for (final source in values) {
      if (source.displayName.toLowerCase() == value.toLowerCase()) {
        return source;
      }
    }

    return null;
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use tryParse instead')
  static CattleSource? fromString(String? value) => tryParse(value);

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all cattle source options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((source) => DropdownOption(
      value: source.value,
      label: source.displayName,
    )).toList();
  }

  /// Get all display names
  static List<String> get displayNames => values.map((s) => s.displayName).toList();

  /// Get all values (enum names)
  static List<String> get allValues => values.map((s) => s.value).toList();
}

/// Wrapper class for nullable values in copyWith methods
/// Allows explicit setting of null values
class _NullableValue<T> {
  final T? value;
  final bool isSet;

  const _NullableValue._(this.value, this.isSet);

  const _NullableValue.set(this.value) : isSet = true;
  const _NullableValue.unset() : value = null, isSet = false;
}

/// Cattle filter parameters for type-safe filtering
class CattleFilterParams {
  // Field name constants to avoid magic strings
  static const String genderKey = 'gender';
  static const String animalTypeKey = 'animalType';
  static const String breedKey = 'breed';
  static const String isActiveKey = 'isActive';

  final Gender? gender;
  final String? animalTypeId;
  final String? breedId;
  final bool? isActive;

  const CattleFilterParams({
    this.gender,
    this.animalTypeId,
    this.breedId,
    this.isActive,
  });

  /// Create filter params from map (useful for form data)
  factory CattleFilterParams.fromMap(Map<String, dynamic> map) {
    return CattleFilterParams(
      gender: Gender.tryParse(map[genderKey] as String?),
      animalTypeId: map[animalTypeKey] as String?,
      breedId: map[breedKey] as String?,
      isActive: map[isActiveKey] as bool?,
    );
  }

  /// Convert to map for API calls
  Map<String, dynamic> toMap() {
    return {
      if (gender != null) genderKey: gender!.value,
      if (animalTypeId != null) animalTypeKey: animalTypeId,
      if (breedId != null) breedKey: breedId,
      if (isActive != null) isActiveKey: isActive,
    };
  }

  /// Create a copy with updated values
  /// Supports explicit null setting using nullable wrappers
  CattleFilterParams copyWith({
    _NullableValue<Gender>? gender,
    _NullableValue<String>? animalTypeId,
    _NullableValue<String>? breedId,
    _NullableValue<bool>? isActive,
  }) {
    return CattleFilterParams(
      gender: gender?.isSet == true ? gender!.value : this.gender,
      animalTypeId: animalTypeId?.isSet == true ? animalTypeId!.value : this.animalTypeId,
      breedId: breedId?.isSet == true ? breedId!.value : this.breedId,
      isActive: isActive?.isSet == true ? isActive!.value : this.isActive,
    );
  }

  /// Convenience methods for common copyWith operations
  CattleFilterParams withGender(Gender? gender) => copyWith(
    gender: _NullableValue.set(gender),
  );

  CattleFilterParams withAnimalType(String? animalTypeId) => copyWith(
    animalTypeId: _NullableValue.set(animalTypeId),
  );

  CattleFilterParams withBreed(String? breedId) => copyWith(
    breedId: _NullableValue.set(breedId),
  );

  CattleFilterParams withActiveStatus(bool? isActive) => copyWith(
    isActive: _NullableValue.set(isActive),
  );

  /// Clear all filters
  CattleFilterParams clearAll() => const CattleFilterParams();

  /// Check if any filters are active
  bool get hasActiveFilters =>
    gender != null || animalTypeId != null || breedId != null || isActive != null;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CattleFilterParams &&
          gender == other.gender &&
          animalTypeId == other.animalTypeId &&
          breedId == other.breedId &&
          isActive == other.isActive;

  @override
  int get hashCode => Object.hash(gender, animalTypeId, breedId, isActive);

  @override
  String toString() => 'CattleFilterParams(gender: $gender, animalTypeId: $animalTypeId, breedId: $breedId, isActive: $isActive)';
}

/// Weight unit options
enum WeightUnit {
  kg('kg'),
  lbs('lbs'),
  grams('grams'),
  pounds('pounds');

  const WeightUnit(this.displayName);

  final String displayName;

  /// Parse weight unit from string value (case-insensitive)
  static WeightUnit? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final unit in values) {
      if (unit.name.toLowerCase() == value.toLowerCase()) {
        return unit;
      }
    }

    // Support common abbreviations
    switch (value.toLowerCase()) {
      case 'kilogram':
      case 'kilograms':
        return WeightUnit.kg;
      case 'pound':
      case 'lb':
        return WeightUnit.lbs;
      case 'gram':
      case 'g':
        return WeightUnit.grams;
      default:
        return null;
    }
  }

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all weight unit options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((unit) => DropdownOption(
      value: unit.value,
      label: unit.displayName,
    )).toList();
  }
}

/// Measurement method options
enum MeasurementMethod {
  scale('Scale'),
  tape('Tape Measure'),
  visualEstimate('Visual Estimate');

  const MeasurementMethod(this.displayName);

  final String displayName;

  /// Parse measurement method from string value (case-insensitive)
  static MeasurementMethod? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final method in values) {
      if (method.name.toLowerCase() == value.toLowerCase()) {
        return method;
      }
    }

    // Support legacy string values
    switch (value.toLowerCase()) {
      case 'visual_estimate':
        return MeasurementMethod.visualEstimate;
      default:
        return null;
    }
  }

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all measurement method options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((method) => DropdownOption(
      value: method.value,
      label: method.displayName,
    )).toList();
  }
}

/// Measurement quality options
enum MeasurementQuality {
  excellent('Excellent'),
  good('Good'),
  fair('Fair'),
  poor('Poor');

  const MeasurementQuality(this.displayName);

  final String displayName;

  /// Parse measurement quality from string value (case-insensitive)
  static MeasurementQuality? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final quality in values) {
      if (quality.name.toLowerCase() == value.toLowerCase()) {
        return quality;
      }
    }
    return null;
  }

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all measurement quality options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((quality) => DropdownOption(
      value: quality.value,
      label: quality.displayName,
    )).toList();
  }
}

/// Health status options
enum HealthStatus {
  healthy('Healthy'),
  sick('Sick'),
  recovering('Recovering'),
  injured('Injured'),
  unknown('Unknown');

  const HealthStatus(this.displayName);

  final String displayName;

  /// Parse health status from string value (case-insensitive)
  static HealthStatus? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final status in values) {
      if (status.name.toLowerCase() == value.toLowerCase()) {
        return status;
      }
    }
    return null;
  }

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all health status options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((status) => DropdownOption(
      value: status.value,
      label: status.displayName,
    )).toList();
  }
}

/// Feeding status options
enum FeedingStatus {
  normal('Normal'),
  increased('Increased'),
  decreased('Decreased'),
  fasting('Fasting'),
  unknown('Unknown');

  const FeedingStatus(this.displayName);

  final String displayName;

  /// Parse feeding status from string value (case-insensitive)
  static FeedingStatus? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final status in values) {
      if (status.name.toLowerCase() == value.toLowerCase()) {
        return status;
      }
    }
    return null;
  }

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all feeding status options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((status) => DropdownOption(
      value: status.value,
      label: status.displayName,
    )).toList();
  }
}

/// Measurement location options
enum MeasurementLocation {
  barn('Barn'),
  pasture('Pasture'),
  feedlot('Feedlot'),
  clinic('Clinic'),
  field('Field'),
  other('Other');

  const MeasurementLocation(this.displayName);

  final String displayName;

  /// Parse measurement location from string value (case-insensitive)
  static MeasurementLocation? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final location in values) {
      if (location.name.toLowerCase() == value.toLowerCase()) {
        return location;
      }
    }
    return null;
  }

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all measurement location options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((location) => DropdownOption(
      value: location.value,
      label: location.displayName,
    )).toList();
  }
}

/// Season options
enum Season {
  spring('Spring'),
  summer('Summer'),
  autumn('Autumn'),
  winter('Winter');

  const Season(this.displayName);

  final String displayName;

  /// Parse season from string value (case-insensitive)
  static Season? tryParse(String? value) {
    if (value == null) return null;

    // Match against stable enum names
    for (final season in values) {
      if (season.name.toLowerCase() == value.toLowerCase()) {
        return season;
      }
    }
    return null;
  }

  /// Convert to string for database storage (uses stable enum name)
  String get value => name;

  /// Get all season options as dropdown options
  static List<DropdownOption> get dropdownOptions {
    return values.map((season) => DropdownOption(
      value: season.value,
      label: season.displayName,
    )).toList();
  }
}

/// Legacy utility class for backward compatibility
/// @deprecated Use the static methods on the enums directly (e.g., Gender.dropdownOptions)
@Deprecated('Use static methods on enums directly (e.g., Gender.dropdownOptions)')
class EnumDropdownUtils {
  /// Convert Gender enum to dropdown options
  @Deprecated('Use Gender.dropdownOptions instead')
  static List<DropdownOption> genderOptions() => Gender.dropdownOptions;

  /// Convert TransactionCategoryType enum to dropdown options
  @Deprecated('Use TransactionCategoryType.dropdownOptions instead')
  static List<DropdownOption> transactionCategoryTypeOptions() => TransactionCategoryType.dropdownOptions;

  /// Convert CattleSource enum to dropdown options
  @Deprecated('Use CattleSource.dropdownOptions instead')
  static List<DropdownOption> cattleSourceOptions() => CattleSource.dropdownOptions;

  /// Convert WeightUnit enum to dropdown options
  @Deprecated('Use WeightUnit.dropdownOptions instead')
  static List<DropdownOption> weightUnitOptions() => WeightUnit.dropdownOptions;

  /// Convert MeasurementMethod enum to dropdown options
  @Deprecated('Use MeasurementMethod.dropdownOptions instead')
  static List<DropdownOption> measurementMethodOptions() => MeasurementMethod.dropdownOptions;
}

/// Breeding method options
enum BreedingMethod {
  ai('Artificial Insemination'),
  natural('Natural Breeding'),
  et('Embryo Transfer'),
  other('Other Method');

  const BreedingMethod(this.displayName);
  final String displayName;

  static BreedingMethod? tryParse(String? value) {
    if (value == null) return null;
    for (final method in values) {
      if (method.name.toLowerCase() == value.toLowerCase()) return method;
    }
    return null;
  }

  String get value => name;
  static List<DropdownOption> get dropdownOptions {
    return values.map((method) => DropdownOption(
      value: method.value,
      label: method.displayName,
    )).toList();
  }
}

/// Breeding status options
enum BreedingStatus {
  pending('Pending'),
  confirmed('Confirmed'),
  completed('Completed'),
  failed('Failed');

  const BreedingStatus(this.displayName);
  final String displayName;

  static BreedingStatus? tryParse(String? value) {
    if (value == null) return null;
    for (final status in values) {
      if (status.name.toLowerCase() == value.toLowerCase()) return status;
    }
    return null;
  }

  String get value => name;
  static List<DropdownOption> get dropdownOptions {
    return values.map((status) => DropdownOption(
      value: status.value,
      label: status.displayName,
    )).toList();
  }
}

/// Event type options
enum EventType {
  healthCheck('Health Check'),
  vaccination('Vaccination'),
  breeding('Breeding'),
  feeding('Feeding'),
  treatment('Treatment'),
  other('Other');

  const EventType(this.displayName);
  final String displayName;

  static EventType? tryParse(String? value) {
    if (value == null) return null;
    for (final type in values) {
      if (type.name.toLowerCase() == value.toLowerCase()) return type;
    }
    return null;
  }

  String get value => name;
  static List<DropdownOption> get dropdownOptions {
    return values.map((type) => DropdownOption(
      value: type.value,
      label: type.displayName,
    )).toList();
  }
}

/// Priority options
enum Priority {
  low('Low'),
  medium('Medium'),
  high('High'),
  critical('Critical');

  const Priority(this.displayName);
  final String displayName;

  static Priority? tryParse(String? value) {
    if (value == null) return null;
    for (final priority in values) {
      if (priority.name.toLowerCase() == value.toLowerCase()) return priority;
    }
    return null;
  }

  String get value => name;
  static List<DropdownOption> get dropdownOptions {
    return values.map((priority) => DropdownOption(
      value: priority.value,
      label: priority.displayName,
    )).toList();
  }
}

/// Milk quality options
enum MilkQuality {
  excellent('Excellent'),
  good('Good'),
  average('Average'),
  poor('Poor');

  const MilkQuality(this.displayName);
  final String displayName;

  static MilkQuality? tryParse(String? value) {
    if (value == null) return null;
    for (final quality in values) {
      if (quality.name.toLowerCase() == value.toLowerCase()) return quality;
    }
    return null;
  }

  String get value => name;
  static List<DropdownOption> get dropdownOptions {
    return values.map((quality) => DropdownOption(
      value: quality.value,
      label: quality.displayName,
    )).toList();
  }
}

/// Unit options
enum Unit {
  liters('Liters'),
  gallons('Gallons');

  const Unit(this.displayName);
  final String displayName;

  static Unit? tryParse(String? value) {
    if (value == null) return null;
    for (final unit in values) {
      if (unit.name.toLowerCase() == value.toLowerCase()) return unit;
    }
    return null;
  }

  String get value => name;
  static List<DropdownOption> get dropdownOptions {
    return values.map((unit) => DropdownOption(
      value: unit.value,
      label: unit.displayName,
    )).toList();
  }
}

/// Payment method options
enum PaymentMethod {
  cash('Cash'),
  bankTransfer('Bank Transfer'),
  mobileMoney('Mobile Money'),
  check('Check'),
  credit('Credit');

  const PaymentMethod(this.displayName);
  final String displayName;

  static PaymentMethod? tryParse(String? value) {
    if (value == null) return null;
    for (final method in values) {
      if (method.name.toLowerCase() == value.toLowerCase()) return method;
    }
    return null;
  }

  String get value => name;
  static List<DropdownOption> get dropdownOptions {
    return values.map((method) => DropdownOption(
      value: method.value,
      label: method.displayName,
    )).toList();
  }
}

/// Payment status options
enum PaymentStatus {
  paid('Paid'),
  pending('Pending'),
  partial('Partial');

  const PaymentStatus(this.displayName);
  final String displayName;

  static PaymentStatus? tryParse(String? value) {
    if (value == null) return null;
    for (final status in values) {
      if (status.name.toLowerCase() == value.toLowerCase()) return status;
    }
    return null;
  }

  String get value => name;
  static List<DropdownOption> get dropdownOptions {
    return values.map((status) => DropdownOption(
      value: status.value,
      label: status.displayName,
    )).toList();
  }
}

/// Common dropdown options utility class
/// Provides centralized access to frequently used dropdown options
class CommonDropdownOptions {
  /// Location options for measurements
  static List<DropdownOption> get locationOptions => MeasurementLocation.dropdownOptions;

  /// Quality options for measurements
  static List<DropdownOption> get qualityOptions => MeasurementQuality.dropdownOptions;

  /// Health status options
  static List<DropdownOption> get healthStatusOptions => HealthStatus.dropdownOptions;

  /// Feeding status options
  static List<DropdownOption> get feedingStatusOptions => FeedingStatus.dropdownOptions;

  /// Season options
  static List<DropdownOption> get seasonOptions => Season.dropdownOptions;

  /// Weight unit options
  static List<DropdownOption> get weightUnitOptions => WeightUnit.dropdownOptions;

  /// Measurement method options
  static List<DropdownOption> get measurementMethodOptions => MeasurementMethod.dropdownOptions;

  /// Gender options
  static List<DropdownOption> get genderOptions => Gender.dropdownOptions;

  /// Cattle source options
  static List<DropdownOption> get cattleSourceOptions => CattleSource.dropdownOptions;

  /// Transaction category type options
  static List<DropdownOption> get transactionCategoryTypeOptions => TransactionCategoryType.dropdownOptions;

  /// Breeding method options
  static List<DropdownOption> get breedingMethodOptions => BreedingMethod.dropdownOptions;

  /// Breeding status options
  static List<DropdownOption> get breedingStatusOptions => BreedingStatus.dropdownOptions;

  /// Event type options
  static List<DropdownOption> get eventTypeOptions => EventType.dropdownOptions;

  /// Priority options
  static List<DropdownOption> get priorityOptions => Priority.dropdownOptions;

  /// Milk quality options
  static List<DropdownOption> get milkQualityOptions => MilkQuality.dropdownOptions;

  /// Unit options
  static List<DropdownOption> get unitOptions => Unit.dropdownOptions;

  /// Payment method options
  static List<DropdownOption> get paymentMethodOptions => PaymentMethod.dropdownOptions;

  /// Payment status options
  static List<DropdownOption> get paymentStatusOptions => PaymentStatus.dropdownOptions;
}
