import 'package:flutter/material.dart';

/// Centralized UI theme service for consistent color management across the app
/// Enforces approved color palettes and excludes banned colors
class UiThemeService {
  static final UiThemeService _instance = UiThemeService._internal();
  factory UiThemeService() => _instance;
  UiThemeService._internal();

  /// Get the UiThemeService instance (Flutter-style 'of' method)
  static UiThemeService of(BuildContext context) {
    return _instance;
  }

  // Approved color palettes (excluding orange, yellow, grey, amber, brown)
  static const List<Color> _primaryPalette = [
    Colors.blue,
    Colors.green,
    Colors.purple,
    Colors.red,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.cyan,
    Colors.deepPurple,
    Colors.lightBlue,
    Colors.lightGreen,
    Colors.deepOrange, // Dark orange is acceptable
  ];

  static const List<Color> _secondaryPalette = [
    Color(0xFF1976D2), // Blue 700
    Color(0xFF388E3C), // Green 700
    Color(0xFF7B1FA2), // Purple 700
    Color(0xFFD32F2F), // Red 700
    Color(0xFF00796B), // Teal 700
    Color(0xFF303F9F), // Indigo 700
    Color(0xFFC2185B), // Pink 700
    Color(0xFF0097A7), // Cyan 700
    Color(0xFF512DA8), // Deep Purple 700
    Color(0xFF1976D2), // Light Blue 700
    Color(0xFF689F38), // Light Green 700
    Color(0xFFE64A19), // Deep Orange 700
  ];

  static const List<Color> _accentPalette = [
    Color(0xFF2196F3), // Blue 500
    Color(0xFF4CAF50), // Green 500
    Color(0xFF9C27B0), // Purple 500
    Color(0xFFF44336), // Red 500
    Color(0xFF009688), // Teal 500
    Color(0xFF3F51B5), // Indigo 500
    Color(0xFFE91E63), // Pink 500
    Color(0xFF00BCD4), // Cyan 500
    Color(0xFF673AB7), // Deep Purple 500
    Color(0xFF03A9F4), // Light Blue 500
    Color(0xFF8BC34A), // Light Green 500
    Color(0xFFFF5722), // Deep Orange 500
  ];

  // Color assignment tracking to avoid repetition within sections
  final Map<String, Map<String, Color>> _sectionColorAssignments = {};
  final Map<String, int> _sectionColorCounters = {};

  /// Get the main app color (teal) for consistency with pregnancy form
  Color getMainColor() {
    return const Color(0xFF2E7D32); // Teal color used throughout the app
  }

  /// Get appropriate icon color for form fields based on field type
  Color getFormFieldIconColor(String fieldKey) {
    // Use specific colors that match pregnancy form styling
    switch (fieldKey) {
      // Weight form fields
      case 'cattle':
        return Colors.brown; // Match pregnancy form
      case 'measurementDate':
        return Colors.orange; // Match pregnancy form
      case 'weight':
        return Colors.blue; // Scale icon
      case 'measurementMethod':
        return Colors.indigo; // Method icon
      case 'measurementQuality':
        return Colors.red; // Star icon
      case 'healthStatus':
        return Colors.orange; // Health icon

      // Health record form fields
      case 'cattleId':
        return Colors.brown; // Match pregnancy form cattle field
      case 'date':
        return Colors.orange; // Match pregnancy form date field
      case 'condition':
        return Colors.red; // Medical condition - red for urgency
      case 'treatment':
        return Colors.green; // Treatment - green for healing
      case 'veterinarian':
        return Colors.blue; // Professional - blue for trust
      case 'cost':
        return Colors.indigo; // Financial - indigo for money

      // Common fields
      case 'notes':
        return Colors.deepOrange; // Match pregnancy form
      default:
        return getMainColor(); // Default to main color
    }
  }

  /// Get a deterministic color for a given key within a section
  /// Ensures no color repetition within the same section
  Color getColor(String key, {String section = 'default', ColorPalette palette = ColorPalette.primary}) {
    // Special case for dialog headers - use main color for consistency
    if (key == 'dialog_header') {
      return getMainColor();
    }

    // For form fields, use specific icon colors
    if (section == 'weight' || section == 'form' || section == 'health') {
      return getFormFieldIconColor(key);
    }

    // Initialize section tracking if needed
    _sectionColorAssignments[section] ??= {};
    _sectionColorCounters[section] ??= 0;

    // Return existing color if already assigned
    if (_sectionColorAssignments[section]!.containsKey(key)) {
      return _sectionColorAssignments[section]![key]!;
    }

    // Get the appropriate color palette
    final colorList = _getColorPalette(palette);

    // Use deterministic hash for consistent color assignment
    final hash = _hashString(key);
    var colorIndex = hash % colorList.length;

    // Ensure no repetition within the section by checking used colors
    final usedColors = _sectionColorAssignments[section]!.values.toSet();
    var attempts = 0;

    while (usedColors.contains(colorList[colorIndex]) && attempts < colorList.length) {
      colorIndex = (colorIndex + 1) % colorList.length;
      attempts++;
    }

    // If all colors are used, start over with counter-based selection
    if (attempts >= colorList.length) {
      colorIndex = _sectionColorCounters[section]! % colorList.length;
      _sectionColorCounters[section] = (_sectionColorCounters[section]! + 1) % colorList.length;
    }

    final selectedColor = colorList[colorIndex];
    _sectionColorAssignments[section]![key] = selectedColor;

    return selectedColor;
  }

  /// Get color palette based on type
  List<Color> _getColorPalette(ColorPalette palette) {
    switch (palette) {
      case ColorPalette.primary:
        return _primaryPalette;
      case ColorPalette.secondary:
        return _secondaryPalette;
      case ColorPalette.accent:
        return _accentPalette;
    }
  }

  /// Generate deterministic hash for string
  int _hashString(String input) {
    var hash = 0;
    for (int i = 0; i < input.length; i++) {
      hash = ((hash << 5) - hash + input.codeUnitAt(i)) & 0xffffffff;
    }
    return hash.abs();
  }

  /// Clear color assignments for a section (useful for dynamic forms)
  void clearSectionColors(String section) {
    _sectionColorAssignments.remove(section);
    _sectionColorCounters.remove(section);
  }

  /// Get contrasting text color (white or black) for a given background color
  Color getContrastingTextColor(Color backgroundColor) {
    // Calculate luminance
    final luminance = backgroundColor.computeLuminance();
    
    // Return white for dark backgrounds, black for light backgrounds
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  /// Get a lighter shade of the given color
  Color getLighterShade(Color color, {double factor = 0.1}) {
    return Color.lerp(color, Colors.white, factor) ?? color;
  }

  /// Get a darker shade of the given color
  Color getDarkerShade(Color color, {double factor = 0.1}) {
    return Color.lerp(color, Colors.black, factor) ?? color;
  }

  /// Get theme data for form fields with consistent styling
  /// Matches the pregnancy form's field styling exactly
  /// Fixed label truncation issue with proper padding and spacing
  InputDecoration getFormFieldDecoration({
    required String label,
    String? hint,
    IconData? icon,
    Color? themeColor,
    String? errorText,
    String? suffix,
    String? prefix,
    bool isRequired = false,
  }) {
    final color = themeColor ?? _primaryPalette[0];

    final decoration = InputDecoration(
      labelText: isRequired ? '$label *' : label,
      hintText: hint,
      errorText: errorText,
      prefixText: prefix,
      suffixText: suffix,
      prefixIcon: icon != null ? Icon(icon, color: color) : null,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        gapPadding: 8, // Add gap padding to prevent label clipping
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        gapPadding: 8, // Add gap padding to prevent label clipping
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: color, width: 2),
        gapPadding: 8, // Add gap padding to prevent label clipping
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red),
        gapPadding: 8, // Add gap padding to prevent label clipping
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red, width: 2),
        gapPadding: 8, // Add gap padding to prevent label clipping
      ),
      // Standard padding for non-floating labels
      contentPadding: const EdgeInsets.fromLTRB(12, 16, 12, 16),
      floatingLabelBehavior: FloatingLabelBehavior.never,
      // Ensure labels are not dense to prevent truncation
      isDense: false,
      // Align label with hint to prevent overlap issues
      alignLabelWithHint: true,
      // Standard height for non-floating labels
      constraints: const BoxConstraints(minHeight: 56),
      // Enhanced label styling to prevent truncation
      labelStyle: TextStyle(
        color: color,
        fontSize: 14, // Reduced to match field content
        fontWeight: FontWeight.w500,
      ),
      // Enhanced floating label styling
      floatingLabelStyle: TextStyle(
        color: color,
        fontSize: 12, // Smaller floating label
        fontWeight: FontWeight.w500,
      ),
      hintStyle: TextStyle(
        color: Colors.grey[600],
        fontSize: 14, // Reduced to match other text sizes
      ),
    );

    return decoration;
  }

  /// Get button theme with consistent styling
  ButtonStyle getButtonStyle({
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
    Size? minimumSize,
  }) {
    final bgColor = backgroundColor ?? _primaryPalette[0];
    final fgColor = foregroundColor ?? getContrastingTextColor(bgColor);
    
    return ElevatedButton.styleFrom(
      backgroundColor: bgColor,
      foregroundColor: fgColor,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      minimumSize: minimumSize ?? const Size(120, 48),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 2,
    );
  }

  /// Get card theme with consistent styling
  CardTheme getCardTheme({Color? color}) {
    return CardTheme(
      color: color,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.all(8),
    );
  }

  /// Validate if a color is approved (not in banned list)
  bool isColorApproved(Color color) {
    // Define banned colors (orange, yellow, grey, amber, brown variations)
    final bannedColors = [
      Colors.orange,
      Colors.yellow,
      Colors.grey,
      Colors.amber,
      Colors.brown,
      // Add more specific banned shades if needed
    ];
    
    return !bannedColors.any((bannedColor) =>
      color.r == bannedColor.r && color.g == bannedColor.g && color.b == bannedColor.b ||
      _isColorSimilar(color, bannedColor)
    );
  }

  /// Check if two colors are similar (within a threshold)
  bool _isColorSimilar(Color color1, Color color2, {double threshold = 0.1}) {
    final hsl1 = HSLColor.fromColor(color1);
    final hsl2 = HSLColor.fromColor(color2);
    
    final hueDiff = (hsl1.hue - hsl2.hue).abs();
    final satDiff = (hsl1.saturation - hsl2.saturation).abs();
    final lightDiff = (hsl1.lightness - hsl2.lightness).abs();
    
    return hueDiff < threshold && satDiff < threshold && lightDiff < threshold;
  }

  /// Get all available colors for a palette
  List<Color> getAvailableColors(ColorPalette palette) {
    return List.from(_getColorPalette(palette));
  }

  /// Reset all color assignments (useful for testing or form resets)
  void resetAllColorAssignments() {
    _sectionColorAssignments.clear();
    _sectionColorCounters.clear();
  }
}

/// Enum for different color palette types
enum ColorPalette {
  primary,
  secondary,
  accent,
}
