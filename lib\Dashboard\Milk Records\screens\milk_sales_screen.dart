import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../dialogs/milk_sale_entry_dialog.dart';
import '../models/milk_sale_isar.dart';
import '../services/milk_sales_service.dart';
import '../services/milk_service.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import 'package:logging/logging.dart';

class MilkSalesScreen extends StatefulWidget {
  const MilkSalesScreen({super.key});

  @override
  State<MilkSalesScreen> createState() => _MilkSalesScreenState();
}

class _MilkSalesScreenState extends State<MilkSalesScreen> {
  final _milkSalesService = MilkSalesService();
  final _milkService = MilkService();
  final _farmSetupHandler = FarmSetupHandler.instance;
  final _logger = Logger('MilkSalesScreen');
  DateTime _selectedDate = DateTime.now();
  List<MilkSaleIsar> _sales = [];
  List<MilkSaleIsar> _allSales = [];
  bool _isLoading = true;
  bool _showAllSales = true;

  // Currency settings
  String _currencySymbol = '\$';
  bool _symbolBeforeAmount = true;

  // Color scheme
  static const _primaryColor = Color(0xFF2E7D32); // Dark Green
  static const _secondaryColor = Color(0xFF1565C0); // Blue
  static const _accentColor = Color(0xFF6A1B9A); // Purple

  @override
  void initState() {
    super.initState();
    _loadCurrencySettings();
    _refreshData();
  }

  // Load currency settings from database
  Future<void> _loadCurrencySettings() async {
    try {
      final currencySettings = await _farmSetupHandler.getCurrencySettings();
      if (mounted) {
        setState(() {
          _currencySymbol = currencySettings.currencySymbol;
          _symbolBeforeAmount = currencySettings.symbolBeforeAmount;
        });
      }
    } catch (e) {
      _logger.warning('Error loading currency settings: $e');
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _currencySymbol = '\$';
          _symbolBeforeAmount = true;
        });
      }
    }
  }

  // Format currency based on settings
  String _formatCurrency(double amount) {
    return _symbolBeforeAmount
        ? '$_currencySymbol${amount.toStringAsFixed(2)}'
        : '${amount.toStringAsFixed(2)}$_currencySymbol';
  }

  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load all sales first
      final allSales = await _milkSalesService.getMilkSales();

      // Then load sales for the selected date
      final salesForDate =
          await _milkSalesService.getMilkSalesForDate(_selectedDate);

      // Check if widget is still mounted before updating state
      if (mounted) {
        setState(() {
          _allSales = allSales;
          _sales = _showAllSales ? allSales : salesForDate;
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.severe('Error loading sales data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  Future<void> _showEntryDialog() async {
    // Calculate available milk before showing the dialog
    final double availableMilk = await _calculateAvailableMilk();

    // Check if widget is still mounted before using the context
    if (!mounted) return;

    final result = await MilkSaleEntryDialog.show(
      context: context,
      selectedDate: _selectedDate,
      availableMilk: availableMilk,
    );

    if (result != null && mounted) {
      _refreshData();
    }
  }

  Future<double> _calculateAvailableMilk() async {
    final records = await _milkService.getMilkRecordsForDate(_selectedDate);
    final totalProduction = records.fold<double>(
        0.0,
        (sum, record) => sum + 
            (record.morningAmount ?? 0) + 
            (record.eveningAmount ?? 0));

    final sales = await _milkSalesService.getMilkSalesForDate(_selectedDate);
    double totalSold = 0.0;
    for (var sale in sales) {
      totalSold += sale.quantity;
    }
    return totalProduction - totalSold;
  }

  void _toggleViewMode() {
    setState(() {
      _showAllSales = !_showAllSales;
      _sales = _showAllSales
          ? _allSales
          : _allSales
              .where((sale) =>
                  sale.date.year == _selectedDate.year &&
                  sale.date.month == _selectedDate.month &&
                  sale.date.day == _selectedDate.day)
              .toList();
    });
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: _primaryColor,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        if (!_showAllSales) {
          _sales = _allSales
              .where((sale) =>
                  sale.date.year == _selectedDate.year &&
                  sale.date.month == _selectedDate.month &&
                  sale.date.day == _selectedDate.day)
              .toList();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Sales Records',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
        backgroundColor: _primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _toggleViewMode,
            tooltip: _showAllSales ? 'Show Today Only' : 'Show All Sales',
          ),
          IconButton(
            icon: const Icon(Icons.calendar_month),
            onPressed: _selectDate,
            tooltip: 'Select Date',
          ),
        ],
      ),
      body: Container(
        color: Colors.white,
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: _primaryColor))
            : Column(
                children: [
                  // 1. Sales Summary header and cards
                  _buildSummaryCards(_sales),

                  // 3. Main context header
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 12.0),
                    color: Colors.grey.shade100,
                    child: Row(
                      children: [
                        Text(
                          _showAllSales
                              ? 'All Sales Records'
                              : 'Sales for ${DateFormat('dd MMM yyyy').format(_selectedDate)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: _primaryColor,
                          ),
                        ),
                        const Spacer(),
                        Chip(
                          label: Text(
                            '${_sales.length} Records',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          backgroundColor: _primaryColor,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                        ),
                      ],
                    ),
                  ),

                  // 4. Sales data table
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Card(
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            _buildListHeader(),
                            Expanded(
                              child: _sales.isEmpty
                                  ? Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.receipt_long,
                                            size: 64,
                                            color: Colors.grey.shade400,
                                          ),
                                          const SizedBox(height: 16),
                                          Text(
                                            'No sales records found',
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          ElevatedButton.icon(
                                            onPressed: _showEntryDialog,
                                            icon: const Icon(Icons.add),
                                            label: const Text('Add Sale'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: _primaryColor,
                                              foregroundColor: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : ListView.separated(
                                      itemCount: _sales.length,
                                      separatorBuilder: (context, index) =>
                                          Divider(
                                        color: Colors.grey.withAlpha(128),
                                        height: 1,
                                      ),
                                      itemBuilder: (context, index) {
                                        final sale = _sales[index];
                                        return _buildSaleCard(sale);
                                      },
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: _primaryColor,
        onPressed: _showEntryDialog,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSummaryCards(List<MilkSaleIsar> sales) {
    final totalSales =
        sales.fold<double>(0.0, (sum, sale) => sum + sale.total);
    final totalQuantity =
        sales.fold<double>(0.0, (sum, sale) => sum + sale.quantity);
    final averageRate =
        totalQuantity > 0 ? (totalSales / totalQuantity).toDouble() : 0.0;
    final totalPaid = sales
        .where((s) => s.paymentStatus == 'Paid')
        .fold<double>(0.0, (sum, sale) => sum + sale.total);

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Summary section header - centered
          const Padding(
            padding: EdgeInsets.only(bottom: 12.0),
            child: Text(
              'Sales Summary',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),
          // Summary cards
          Row(
            children: [
              _buildSummaryCard(
                title: 'Total Sales',
                value: _formatCurrency(totalSales),
                icon: Icons.attach_money, // Use generic money icon instead of currency-specific
                color: _primaryColor,
              ),
              const SizedBox(width: 16),
              _buildSummaryCard(
                title: 'Avg Rate/L',
                value: _formatCurrency(averageRate),
                icon: Icons.trending_up,
                color: _secondaryColor,
              ),
              const SizedBox(width: 16),
              _buildSummaryCard(
                title: 'Paid Amount',
                value: _formatCurrency(totalPaid),
                icon: Icons.verified_user,
                color: _accentColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
      {required String title,
      required String value,
      required IconData icon,
      required Color color}) {
    return Expanded(
      child: Container(
        height: 100, // Increased height
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(26),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 18),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                    softWrap: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Expanded(
              child: Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 18,
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      decoration: BoxDecoration(
        color: _primaryColor.withAlpha(25),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: const Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'Date',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Quantity',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 10), // Add extra space between Quantity and Amount
          Expanded(
            flex: 2,
            child: Text(
              'Amount',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(width: 10), // Add some padding to the left
                Text(
                  'Status',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaleCard(MilkSaleIsar sale) {
    // Get the formatted date
    final dateStr = DateFormat('MMMM dd, yyyy').format(sale.date);
    
    // Format payment status
    final isPaid = sale.paymentStatus == 'Paid';
    
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  dateStr,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isPaid ? Colors.green.shade100 : Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isPaid ? 'Paid' : 'Pending',
                    style: TextStyle(
                      color: isPaid ? Colors.green.shade800 : Colors.orange.shade800,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildSaleDetailItem(
                  Icons.water_drop,
                  'Quantity',
                  '${sale.quantity.toStringAsFixed(1)} L',
                  Colors.blue,
                ),
                const SizedBox(width: 16),
                _buildSaleDetailItem(
                  Icons.attach_money,
                  'Rate',
                  _formatCurrency(sale.price),
                  _secondaryColor,
                ),
                const SizedBox(width: 16),
                _buildSaleDetailItem(
                  Icons.account_balance_wallet,
                  'Total',
                  _formatCurrency(sale.total),
                  _primaryColor,
                  isHighlighted: true,
                ),
              ],
            ),
            if (sale.notes != null && sale.notes!.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 4),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.note, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      sale.notes!,
                      style: const TextStyle(
                        fontSize: 13,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSaleDetailItem(IconData icon, String label, String value, Color color, {bool isHighlighted = false}) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
        decoration: BoxDecoration(
          color: isHighlighted ? Colors.grey.withAlpha(64) : null,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isHighlighted ? Colors.grey.withAlpha(128) : Colors.transparent,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 18),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
                softWrap: true,
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
