import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/category_isar.dart';
import '../services/transactions_handler.dart';
import '../transactions_tabs/transactions_list_tab.dart';
import '../transactions_tabs/summary_tab.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../../../utils/message_utils.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({Key? key}) : super(key: key);

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  final TransactionsHandler _transactionsHandler =
      GetIt.instance<TransactionsHandler>();
  bool _isLoading = true;
  List<CategoryIsar> _categories = [];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _transactionsHandler.getAllCategories();
      if (!mounted) return;
      setState(() {
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading categories: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        FinancialMessageUtils.showError(context, 'Error loading categories');
      }
    }
  }

  void _showAddTransactionDialog() async {
    await TransactionFormDialog.show(
      context: context,
      categories: _categories,
      onSave: (transaction) async {
        try {
          await _transactionsHandler.addTransaction(transaction);

          // Check mounted status after await
          if (!mounted) return;

          // Use standardized success message
          if (context.mounted) {
            FinancialMessageUtils.showSuccess(context,
                FinancialMessageUtils.expenseRecorded());
          }

        } catch (e) {
          debugPrint('Error adding transaction: $e');

          // Check mounted status again in catch block
          if (!mounted) return;

          // Use standardized error message
          if (context.mounted) {
            FinancialMessageUtils.showError(context, 'Error adding transaction');
          }
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Transactions'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'List'),
              Tab(text: 'Summary'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            TransactionsListTab(
              onRefresh: () {
                _loadCategories();
              },
            ),
            const SummaryTab(),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _showAddTransactionDialog,
          child: const Icon(Icons.add),
        ),
      ),
    );
  }
}
