import 'package:flutter/material.dart';
import 'form_config.dart';
import 'universal_form_controller.dart';
import 'config/ui_theme_service.dart';
import 'form_fields/text_field.dart';
import 'form_fields/dropdown_field.dart';
import 'form_fields/date_field.dart';
import 'form_fields/checkbox_field.dart';
import 'form_fields/slider_field.dart';

/// Responsive mode for different screen sizes
enum ResponsiveMode {
  mobile,   // < 480px - Single column, compact spacing
  tablet,   // 480-768px - 1-2 columns, medium spacing
  desktop,  // 768-1200px - 2-3 columns, standard spacing
  wide,     // > 1200px - 3+ columns, generous spacing
}

/// Wrapper widget that handles field visibility with optimal performance
/// Only rebuilds when dependencies change, not on every form update
class _FormFieldVisibilityWrapper extends StatelessWidget {
  final UniversalFormController controller;
  final FormFieldConfig config;
  final Widget child;

  const _FormFieldVisibilityWrapper({
    Key? key,
    required this.controller,
    required this.config,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, _) {
        final bool isVisible = _isFieldVisible(config, controller);

        // Smooth animation for field appearance/disappearance
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (child, animation) {
            return SizeTransition(
              sizeFactor: animation,
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
          child: isVisible
            ? child
            : const SizedBox.shrink(key: ValueKey('hidden')),
        );
      },
    );
  }

  /// Check if field should be visible based on dependencies
  /// Static method for optimal performance - no widget context needed
  static bool _isFieldVisible(FormFieldConfig field, UniversalFormController controller) {
    // Early return for basic visibility
    if (!field.visible) return false;

    // Early return if no dependencies
    if (field.dependencies == null || field.dependencies!.isEmpty) {
      return true;
    }

    // Check all dependencies - all must be satisfied
    for (final dependency in field.dependencies!) {
      if (!_isDependencySatisfied(dependency, field, controller)) {
        return false;
      }
    }

    return true;
  }

  /// Check if a single dependency is satisfied
  static bool _isDependencySatisfied(
    FieldDependency dependency,
    FormFieldConfig field,
    UniversalFormController controller,
  ) {
    final parentValue = controller.getValue(dependency.parentFieldKey.value);

    // Custom condition takes precedence
    if (dependency.evaluate != null) {
      final formValues = controller.getAllValues();
      return dependency.evaluate!(formValues);
    }

    // Specific value matching
    if (dependency.parentValue != null) {
      return _valuesMatch(parentValue, dependency.parentValue);
    }

    // Default: check if parent has any meaningful value
    return _hasValue(parentValue);
  }

  /// Check if two values match, handling different types robustly
  static bool _valuesMatch(dynamic value1, dynamic value2) {
    if (value1 == value2) return true;

    // Handle string comparison case-insensitively
    if (value1 is String && value2 is String) {
      return value1.toLowerCase() == value2.toLowerCase();
    }

    return false;
  }

  /// Check if a value is considered "meaningful" (not empty/null/false)
  static bool _hasValue(dynamic value) {
    if (value == null) return false;
    if (value is String) return value.isNotEmpty;
    if (value is bool) return value;
    if (value is List) return value.isNotEmpty;
    if (value is Map) return value.isNotEmpty;
    return true; // For other types, assume non-null means has value
  }
}

/// Factory class for creating form field widgets
/// Eliminates code duplication in the switch statement
class _FormFieldFactory {
  /// Create a form field widget based on the field type
  static Widget createField({
    required FormFieldType type,
    required UniversalFormController controller,
    required FormFieldConfig config,
    required Color themeColor,
    required Widget Function() onBuildCattleSelector,
    required Widget Function() onBuildCustom,
    required Widget Function() onBuildUnsupported,
  }) {
    // Group text-based fields that all use UniversalTextField
    const textFieldTypes = {
      FormFieldType.text,
      FormFieldType.number,
      FormFieldType.currency,
      FormFieldType.percentage,
      FormFieldType.phone,
      FormFieldType.email,
      FormFieldType.url,
      FormFieldType.multiline,
    };

    // Group date-based fields that all use UniversalDateField
    const dateFieldTypes = {
      FormFieldType.date,
      FormFieldType.dateTime,
      FormFieldType.time,
    };

    if (textFieldTypes.contains(type)) {
      return UniversalTextField(
        controller: controller,
        config: config,
        themeColor: themeColor,
      );
    }

    if (dateFieldTypes.contains(type)) {
      return UniversalDateField(
        controller: controller,
        config: config,
        themeColor: themeColor,
      );
    }

    // Handle individual field types
    switch (type) {
      case FormFieldType.dropdown:
        return UniversalDropdownField(
          controller: controller,
          config: config,
          themeColor: themeColor,
        );

      case FormFieldType.checkbox:
        return UniversalCheckboxField(
          controller: controller,
          config: config,
          themeColor: themeColor,
        );

      case FormFieldType.slider:
        return UniversalSliderField(
          controller: controller,
          config: config,
          themeColor: themeColor,
        );

      case FormFieldType.cattleSelector:
        return onBuildCattleSelector();

      case FormFieldType.custom:
        return onBuildCustom();

      default:
        return onBuildUnsupported();
    }
  }
}

/// Universal form builder that creates responsive, multi-column layouts
/// with dynamic theming and field dependency handling
class UniversalFormBuilder extends StatefulWidget {
  final UniversalFormController controller;
  final List<FormFieldConfig> fields;
  final String section;
  final EdgeInsets? padding;
  final double? spacing;
  final bool scrollable;

  const UniversalFormBuilder({
    Key? key,
    required this.controller,
    required this.fields,
    this.section = 'default',
    this.padding,
    this.spacing,
    this.scrollable = false,
  }) : super(key: key);

  @override
  State<UniversalFormBuilder> createState() => _UniversalFormBuilderState();
}

class _UniversalFormBuilderState extends State<UniversalFormBuilder> {
  late Map<String, Color> _fieldColors;

  @override
  void initState() {
    super.initState();
    _initializeFieldColors();
  }

  @override
  void didUpdateWidget(UniversalFormBuilder oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Re-initialize colors if fields or section changed
    if (widget.fields != oldWidget.fields || widget.section != oldWidget.section) {
      _initializeFieldColors();
    }
  }

  /// Initialize colors for all fields in this section
  void _initializeFieldColors() {
    final uiTheme = UiThemeService.of(context);
    _fieldColors = {};
    for (final field in widget.fields) {
      _fieldColors[field.key.value] = uiTheme.getColor(
        field.key.value,
        section: widget.section,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Enhanced responsive breakpoints
        final screenWidth = constraints.maxWidth;
        final ResponsiveMode mode;

        if (screenWidth < 480) {
          mode = ResponsiveMode.mobile;
        } else if (screenWidth < 768) {
          mode = ResponsiveMode.tablet;
        } else if (screenWidth < 1200) {
          mode = ResponsiveMode.desktop;
        } else {
          mode = ResponsiveMode.wide;
        }

        return _buildResponsiveForm(context, mode);
      },
    );
  }

  /// Build responsive form layout with enhanced responsive behavior
  Widget _buildResponsiveForm(BuildContext context, ResponsiveMode mode) {
    final rows = _groupFieldsIntoRows(widget.fields, mode);
    final spacing = _getSpacingForMode(mode);
    final padding = _getPaddingForMode(mode);

    final formContent = Padding(
      padding: widget.padding ?? padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: rows.map((row) => _buildFormRow(context, row, mode, spacing)).toList(),
      ),
    );

    // Wrap in SingleChildScrollView if scrollable is enabled
    if (widget.scrollable) {
      return SingleChildScrollView(
        padding: EdgeInsets.symmetric(vertical: spacing / 2),
        clipBehavior: Clip.none, // Prevent clipping of floating labels
        child: formContent,
      );
    }

    return formContent;
  }

  /// Get spacing based on responsive mode
  double _getSpacingForMode(ResponsiveMode mode) {
    switch (mode) {
      case ResponsiveMode.mobile:
        return 12.0; // Compact spacing for mobile
      case ResponsiveMode.tablet:
        return 16.0; // Standard spacing for tablet
      case ResponsiveMode.desktop:
        return 20.0; // Generous spacing for desktop
      case ResponsiveMode.wide:
        return 24.0; // Extra spacing for wide screens
    }
  }

  /// Get padding based on responsive mode
  EdgeInsets _getPaddingForMode(ResponsiveMode mode) {
    switch (mode) {
      case ResponsiveMode.mobile:
        return const EdgeInsets.all(12.0);
      case ResponsiveMode.tablet:
        return const EdgeInsets.all(16.0);
      case ResponsiveMode.desktop:
        return const EdgeInsets.all(20.0);
      case ResponsiveMode.wide:
        return const EdgeInsets.all(24.0);
    }
  }

  /// Group fields into rows based on responsive mode and columnSpan
  List<List<FormFieldConfig>> _groupFieldsIntoRows(
    List<FormFieldConfig> fields,
    ResponsiveMode mode,
  ) {
    final rows = <List<FormFieldConfig>>[];
    var currentRow = <FormFieldConfig>[];
    var currentRowSpan = 0.0;

    for (final field in fields) {
      final fieldSpan = _getEffectiveColumnSpan(field, mode);

      // If adding this field would exceed row capacity, start new row
      if (currentRowSpan + fieldSpan > 1.0 && currentRow.isNotEmpty) {
        rows.add(List.from(currentRow));
        currentRow.clear();
        currentRowSpan = 0.0;
      }

      currentRow.add(field);
      currentRowSpan += fieldSpan;

      // If row is full, start new row
      if (currentRowSpan >= 1.0) {
        rows.add(List.from(currentRow));
        currentRow.clear();
        currentRowSpan = 0.0;
      }
    }

    // Add remaining fields
    if (currentRow.isNotEmpty) {
      rows.add(currentRow);
    }

    return rows;
  }

  /// Get effective column span based on responsive mode
  double _getEffectiveColumnSpan(FormFieldConfig field, ResponsiveMode mode) {
    switch (mode) {
      case ResponsiveMode.mobile:
        // Mobile: All fields take full width except checkboxes
        return field.type == FormFieldType.checkbox ? 0.5 : 1.0;

      case ResponsiveMode.tablet:
        // Tablet: Respect original span but cap at 1.0 for very wide fields
        return field.columnSpan > 1.0 ? 1.0 : field.columnSpan;

      case ResponsiveMode.desktop:
        // Desktop: Use original column span
        return field.columnSpan;

      case ResponsiveMode.wide:
        // Wide screens: Allow smaller minimum spans for more columns
        return field.columnSpan < 0.25 ? 0.25 : field.columnSpan;
    }
  }

  /// Build a single form row with responsive layout
  Widget _buildFormRow(
    BuildContext context,
    List<FormFieldConfig> rowFields,
    ResponsiveMode mode,
    double spacing,
  ) {
    if (rowFields.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: EdgeInsets.only(bottom: spacing),
      child: mode == ResponsiveMode.mobile
          ? _buildMobileColumn(context, rowFields, spacing)
          : _buildResponsiveRow(context, rowFields, mode),
    );
  }

  /// Build mobile column layout (single column)
  Widget _buildMobileColumn(
    BuildContext context,
    List<FormFieldConfig> rowFields,
    double spacing,
  ) {
    return Column(
      children: rowFields
          .map((field) => Padding(
                padding: EdgeInsets.only(bottom: spacing / 2),
                child: _FormFieldVisibilityWrapper(
                  key: ValueKey(field.key.value),
                  controller: widget.controller,
                  config: field,
                  child: _buildField(context, field),
                ),
              ))
          .toList(),
    );
  }

  /// Build responsive row layout (multi-column)
  Widget _buildResponsiveRow(
    BuildContext context,
    List<FormFieldConfig> rowFields,
    ResponsiveMode mode,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _buildRowChildren(context, rowFields, mode),
    );
  }

  /// Build children for a row with responsive spacing and constraints
  List<Widget> _buildRowChildren(
    BuildContext context,
    List<FormFieldConfig> rowFields,
    ResponsiveMode mode,
  ) {
    final children = <Widget>[];
    final horizontalSpacing = _getHorizontalSpacing(mode);
    final minWidth = _getMinWidth(mode);

    for (int i = 0; i < rowFields.length; i++) {
      final field = rowFields[i];
      final effectiveSpan = _getEffectiveColumnSpan(field, mode);

      children.add(
        Flexible(
          flex: (effectiveSpan * 100).round(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: minWidth,
            ),
            child: _FormFieldVisibilityWrapper(
              key: ValueKey(field.key.value),
              controller: widget.controller,
              config: field,
              child: _buildField(context, field),
            ),
          ),
        ),
      );

      // Add spacing between fields (except for last field)
      if (i < rowFields.length - 1) {
        children.add(SizedBox(width: horizontalSpacing));
      }
    }

    return children;
  }

  /// Get horizontal spacing between fields based on mode
  double _getHorizontalSpacing(ResponsiveMode mode) {
    switch (mode) {
      case ResponsiveMode.mobile:
        return 8.0;
      case ResponsiveMode.tablet:
        return 12.0;
      case ResponsiveMode.desktop:
        return 16.0;
      case ResponsiveMode.wide:
        return 20.0;
    }
  }

  /// Get minimum width for fields based on mode
  double _getMinWidth(ResponsiveMode mode) {
    switch (mode) {
      case ResponsiveMode.mobile:
        return 120.0; // Smaller minimum for mobile
      case ResponsiveMode.tablet:
        return 150.0; // Standard minimum
      case ResponsiveMode.desktop:
        return 180.0; // Larger minimum for desktop
      case ResponsiveMode.wide:
        return 200.0; // Generous minimum for wide screens
    }
  }

  /// Build individual field widget using factory pattern
  /// Visibility is now handled by wrapper - this method focuses on field creation
  Widget _buildField(BuildContext context, FormFieldConfig field) {
    try {
      final themeColor = _fieldColors[field.key.value] ?? Colors.blue;

      // Use factory pattern to eliminate switch statement duplication
      return _FormFieldFactory.createField(
        type: field.type,
        controller: widget.controller,
        config: field,
        themeColor: themeColor,
        onBuildCattleSelector: () => _buildCattleSelectorField(field, themeColor),
        onBuildCustom: () => _buildCustomField(field, themeColor),
        onBuildUnsupported: () => _buildUnsupportedField(field),
      );
    } catch (error, stackTrace) {
      // Log error for debugging
      debugPrint('Error building field ${field.key.value}: $error');
      debugPrint('Stack trace: $stackTrace');

      // Return error widget instead of crashing
      return _buildErrorField(field, error);
    }
  }

  /// Build cattle selector field (placeholder for now)
  Widget _buildCattleSelectorField(FormFieldConfig field, Color themeColor) {
    // This would be implemented as a specialized dropdown that loads cattle data
    // For now, return a placeholder with better styling
    return _buildPlaceholderField(
      field: field,
      themeColor: themeColor,
      icon: Icons.pets,
      message: '${field.label} (Coming Soon)',
      backgroundColor: themeColor.withValues(alpha: 0.05),
    );
  }

  /// Build custom field
  Widget _buildCustomField(FormFieldConfig field, Color themeColor) {
    // Custom fields would be handled by the customProperties
    return _buildPlaceholderField(
      field: field,
      themeColor: themeColor,
      icon: Icons.extension,
      message: 'Custom Field: ${field.label}',
      backgroundColor: themeColor.withValues(alpha: 0.05),
    );
  }

  /// Build unsupported field placeholder
  Widget _buildUnsupportedField(FormFieldConfig field) {
    return _buildPlaceholderField(
      field: field,
      themeColor: Colors.orange,
      icon: Icons.warning,
      message: 'Unsupported field type: ${field.type}',
      backgroundColor: Colors.orange.withValues(alpha: 0.05),
    );
  }

  /// Build error field when field creation fails
  Widget _buildErrorField(FormFieldConfig field, Object error) {
    return _buildPlaceholderField(
      field: field,
      themeColor: Colors.red,
      icon: Icons.error,
      message: 'Error: ${field.label} failed to load',
      backgroundColor: Colors.red.withValues(alpha: 0.05),
      subtitle: error.toString(),
    );
  }

  /// Build a consistent placeholder field widget
  Widget _buildPlaceholderField({
    required FormFieldConfig field,
    required Color themeColor,
    required IconData icon,
    required String message,
    required Color backgroundColor,
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(color: themeColor.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(icon, color: themeColor, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  message,
                  style: TextStyle(
                    color: themeColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: themeColor.withValues(alpha: 0.7),
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }


}
