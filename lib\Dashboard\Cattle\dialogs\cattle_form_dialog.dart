import 'package:flutter/material.dart';
import '../../widgets/dialogs/index.dart';
import '../models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../utils/message_utils.dart';

/// Ultra-clean facade using centralized ModuleFormConfigs
class CattleFormDialog {
  static Future<void> show({
    required BuildContext context,
    CattleIsar? cattle,
    required List<AnimalTypeIsar> animalTypes,
    required List<CattleIsar> existingCattle,
    required Function(CattleIsar) onSave,
    required String businessId,
  }) async {
    const formConfigs = ModuleFormConfigs();

    await UniversalFormDialog.show<CattleIsar>(
      context: context,
      config: FormConfig.simple(
        title: cattle == null ? 'Add Cattle' : 'Edit Cattle',
        fields: formConfigs.forCattle(),
      ),
      initialValues: cattle != null ? {
        FormKeys.tagId.value: cattle.tagId ?? '',
        FormKeys.name.value: cattle.name ?? '',
        FormKeys.animalType.value: cattle.animalTypeId ?? '',
        FormKeys.gender.value: cattle.gender ?? '',
        FormKeys.breed.value: cattle.breedId ?? '',
        FormKeys.source.value: cattle.source ?? CattleSource.purchased.value,
        FormKeys.dateOfBirth.value: cattle.dateOfBirth,
        FormKeys.motherTagId.value: cattle.motherTagId ?? '',
        FormKeys.purchaseDate.value: cattle.purchaseDate,
        FormKeys.purchasePrice.value: cattle.purchasePrice ?? 0.0,
        FormKeys.weight.value: cattle.weight ?? 0.0,
        FormKeys.color.value: cattle.color ?? '',
        FormKeys.notes.value: cattle.notes ?? '',
        FormKeys.autoGenerateTagId.value: false,
      } : {
        FormKeys.autoGenerateTagId.value: true,
        FormKeys.source.value: CattleSource.purchased.value,
      },
      section: 'cattle',
      closeOnSave: true,
      onSave: (values) async {
        try {
          final record = cattle ?? CattleIsar();
          record.businessId = businessId;
          record.tagId = values[FormKeys.tagId.value]?.toString().trim();
          record.name = values[FormKeys.name.value]?.toString().trim();
          record.animalTypeId = values[FormKeys.animalType.value]?.toString();
          record.breedId = values[FormKeys.breed.value]?.toString();
          record.gender = values[FormKeys.gender.value]?.toString();
          record.source = values[FormKeys.source.value]?.toString();
          record.dateOfBirth = values[FormKeys.dateOfBirth.value] as DateTime?;
          record.motherTagId = values[FormKeys.motherTagId.value]?.toString();
          record.purchaseDate = values[FormKeys.purchaseDate.value] as DateTime?;
          record.purchasePrice = values[FormKeys.purchasePrice.value] as double?;
          record.weight = values[FormKeys.weight.value] as double?;
          record.color = values[FormKeys.color.value]?.toString().trim();
          record.notes = values[FormKeys.notes.value]?.toString().trim();
          record.updatedAt = DateTime.now();

          if (cattle == null) {
            record.createdAt = DateTime.now();
            record.status = 'Active';
          }

          onSave(record);
          return record;
        } catch (e) {
          CattleMessageUtils.showError(context, 'Failed to save cattle record.');
          return false;
        }
      },
    );
  }
}
