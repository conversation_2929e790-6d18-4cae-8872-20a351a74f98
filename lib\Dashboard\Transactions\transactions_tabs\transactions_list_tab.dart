// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import '../services/transactions_handler.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../../../utils/message_utils.dart';

class TransactionsListTab extends StatefulWidget {
  final VoidCallback onRefresh;

  const TransactionsListTab({Key? key, required this.onRefresh})
      : super(key: key);

  @override
  State<TransactionsListTab> createState() => _TransactionsListTabState();
}

class _TransactionsListTabState extends State<TransactionsListTab> {
  final TransactionsHandler _transactionsHandler =
      GetIt.instance<TransactionsHandler>();
  List<TransactionIsar> _transactions = [];
  List<CategoryIsar> _categories = [];
  bool _isLoading = true;
  final _searchQuery = TextEditingController();

  // For date filtering

  // Currency settings

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final transactions = await _transactionsHandler.getAllTransactions();
      final categories = await _transactionsHandler.getAllCategories();

      if (!mounted) return;

      setState(() {
        _transactions = transactions;
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading transactions: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        FinancialMessageUtils.showError(context, 'Error loading transactions');
      }
    }
  }

  @override
  void dispose() {
    _searchQuery.dispose();
    super.dispose();
  }

  Future<void> _showAddTransactionDialog() async {
    await TransactionFormDialog.show(
      context: context,
      categories: _categories,
      onSave: (transaction) async {
        try {
          await _transactionsHandler.addTransaction(transaction);
          await _loadData();
        } catch (e) {
          debugPrint('Error adding transaction: $e');
          if (mounted) {
            FinancialMessageUtils.showError(context, 'Error adding transaction');
          }
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('No transactions found'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _showAddTransactionDialog,
              child: const Text('Add Transaction'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _transactions.length,
      itemBuilder: (context, index) {
        final transaction = _transactions[index];
        _categories.firstWhere(
          (c) => c.name == transaction.category,
          orElse: () => CategoryIsar()..name = 'Unknown',
        );

        return ListTile(
          leading: CircleAvatar(
            backgroundColor: transaction.categoryType == 'Income'
                ? Colors.green
                : Colors.red,
            child: Icon(
              transaction.categoryType == 'Income'
                  ? Icons.arrow_upward
                  : Icons.arrow_downward,
              color: Colors.white,
            ),
          ),
          title: Text(transaction.category),
          subtitle: Text(transaction.description),
          trailing: Text(
            '\$${transaction.amount.toStringAsFixed(2)}',
            style: TextStyle(
              color: transaction.categoryType == 'Income'
                  ? Colors.green
                  : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          onTap: () {
            TransactionFormDialog.show(
              context: context,
              transaction: transaction,
              categories: _categories,
              onSave: (updatedTransaction) async {
                try {
                  await _transactionsHandler
                      .updateTransaction(updatedTransaction);
                  await _loadData();
                } catch (e) {
                  debugPrint('Error updating transaction: $e');
                  if (mounted) {
                    FinancialMessageUtils.showError(context,
                        'Error updating transaction');
                  }
                }
              },
            );
          },
        );
      },
    );
  }
}
