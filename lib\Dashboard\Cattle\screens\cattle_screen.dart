import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';
import '../../../constants/app_bar.dart';
import '../models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import 'package:get_it/get_it.dart';
import '../services/cattle_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import 'cattle_detail_screen.dart';
import '../dialogs/cattle_form_dialog.dart';
import '../../../utils/message_utils.dart';

// String constants to avoid hard-coded text
class _Strings {
  static const title = 'Cattle Management';
  static const reports = 'View Cattle Reports';
  static const refresh = 'Refresh';
  static const noRecords = 'No cattle records found';
  static const addFirstPrompt =
      'Add your first cattle using the + button below';
  static const addCattle = 'Add Cattle';
  static const addingCattle = 'Adding cattle...';
  static const unknown = 'Unknown';
  static const tagId = 'Tag ID:';
  static const gender = 'Gender:';
  static const noTagId = 'No Tag ID';
  static const unnamed = 'Unnamed Cattle';

  // Error messages
  static const loadError = 'Failed to load data. Please try again.';
  static const cattleLoadError = 'Failed to load cattle records';
  static const animalTypeLoadError = 'Failed to load animal types';
  static const addError = 'Failed to add cattle. Please try again.';
}

class CattleScreen extends StatefulWidget {
  const CattleScreen({Key? key}) : super(key: key);

  @override
  State<CattleScreen> createState() => _CattleScreenState();
}

class _CattleScreenState extends State<CattleScreen> {
  final List<CattleIsar> _cattle = [];
  bool _isLoading = true;
  final String businessId = 'default_business_id';

  List<AnimalTypeIsar> animalTypes = [];

  // Remove the unused breeds list
  final CattleHandler _cattleHandler = GetIt.instance<CattleHandler>();
  final FarmSetupHandler _farmSetupHandler = GetIt.instance<FarmSetupHandler>();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Future.wait([
        _loadCattle(),
        _loadAnimalTypes(),
      ]);

      setState(() {
        _isLoading = false;
      });
    } catch (e, s) {
      debugPrint('Error loading cattle data: $e\n$s');
      _showErrorSnackBar(_Strings.loadError);
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCattle() async {
    try {
      final cattleData = await _cattleHandler.getAllCattle();
      setState(() {
        _cattle.clear();
        _cattle.addAll(cattleData);
      });
    } catch (e, s) {
      debugPrint('Error loading cattle: $e\n$s');
      _showErrorSnackBar(_Strings.cattleLoadError);
    }
  }

  Future<void> _loadAnimalTypes() async {
    try {
      final animalTypesData = await _farmSetupHandler.getAllAnimalTypes();
      setState(() {
        animalTypes = animalTypesData;
      });
    } catch (e, s) {
      debugPrint('Error loading animal types: $e\n$s');
      _showErrorSnackBar(_Strings.animalTypeLoadError);
    }
  }

  // Remove the _loadBreeds method since it's not used and the dialog will handle breeds internally

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    CattleMessageUtils.showError(context, message);
  }



  void _showAddCattleDialog() {
    CattleFormDialog.show(
      context: context,
      cattle: null, // Use null to indicate this is a new cattle creation
      existingCattle: _cattle,
      businessId: businessId,
      animalTypes: animalTypes,
      onSave: (newCattle) async {
        try {
          // Dialog closes automatically with closeOnSave: true

            // Show loading indicator
            if (!mounted) return;
            CattleMessageUtils.showInfo(context, _Strings.addingCattle);

            await _cattleHandler.addCattle(newCattle);

            if (!mounted) return;

            // Optimized: Add the new cattle to the list instead of reloading
            setState(() {
              _cattle.add(newCattle);
              // Sort the list if needed here
              _cattle.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
            });

            CattleMessageUtils.showSuccess(context, CattleMessageUtils.cattleRecordCreated());
          } catch (e, s) {
            debugPrint('Error adding cattle: $e\n$s');
            if (!mounted) return;
            _showErrorSnackBar(_Strings.addError);
          }
        },
    );
  }

  void _navigateToCattleDetail(CattleIsar cattle) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CattleDetailScreen(
          existingCattle: cattle,
          businessId: businessId,
          onCattleUpdated: (updatedCattle) {
            // Optimized: Update the specific cattle in the list instead of reloading
            if (!mounted) return;

            setState(() {
              final index = _cattle
                  .indexWhere((c) => c.businessId == updatedCattle.businessId);

              if (index >= 0) {
                _cattle[index] = updatedCattle;
                // Sort the list if needed
                _cattle.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
              }
            });
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarConfig.standard(
        title: _Strings.title,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.cattleReport,
            ),
            tooltip: _Strings.reports,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: _Strings.refresh,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _cattle.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.pets_outlined,
                        size: 64,
                        color: Theme.of(context).disabledColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _Strings.noRecords,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _Strings.addFirstPrompt,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: _showAddCattleDialog,
                        icon: const Icon(Icons.add),
                        label: const Text(_Strings.addCattle),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _cattle.length,
                  itemBuilder: (context, index) {
                    final cattle = _cattle[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          child: Text(
                            cattle.name?.isNotEmpty == true
                                ? cattle.name![0].toUpperCase()
                                : '?',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimary,
                            ),
                          ),
                        ),
                        title: Text(cattle.name ?? _Strings.unnamed),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                '${_Strings.tagId} ${cattle.tagId?.toUpperCase() ?? _Strings.noTagId}'),
                            Text(
                                '${_Strings.gender} ${cattle.gender ?? _Strings.unknown}'),
                          ],
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.arrow_forward_ios),
                          onPressed: () => _navigateToCattleDetail(cattle),
                        ),
                        onTap: () => _navigateToCattleDetail(cattle),
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCattleDialog,
        tooltip: _Strings.addCattle,
        child: const Icon(Icons.add),
      ),
    );
  }
}
