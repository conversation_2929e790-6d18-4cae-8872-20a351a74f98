import 'package:flutter/material.dart';
import '../../../widgets/fab_styles.dart';
import '../../../widgets/reusable_tab_bar.dart';
import '../controllers/weight_controller.dart';
import '../dialogs/weight_form_dialog.dart';
import '../tabs/weight_records_tab.dart';
import '../tabs/weight_analytics_tab.dart';
import '../tabs/weight_insights_tab.dart';


class WeightScreen extends StatefulWidget {
  const WeightScreen({Key? key}) : super(key: key);

  @override
  State<WeightScreen> createState() => _WeightScreenState();
}

class _WeightScreenState extends State<WeightScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  late WeightController _weightController;

  // Tab configuration using the reusable widget
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();

    // Define tabs using the reusable widget configuration
    _tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab2Label: 'Records',
      tab2Icon: Icons.list,
      tab3Label: 'Insights',
      tab3Icon: Icons.insights,
    );

    // Initialize tab controller with dynamic length
    _tabController = TabController(length: _tabs.length, vsync: this);

    // Add listener to rebuild FAB when tab changes
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update FAB visibility
    });

    // Initialize weight controller and load data
    _weightController = WeightController();
    _weightController.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Weight Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _weightController.refresh();
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
          ),
          // Tab Views
          Expanded(
            child: ListenableBuilder(
              listenable: _weightController,
              builder: (context, child) {
                if (_weightController.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (_weightController.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                        const SizedBox(height: 16),
                        Text(
                          _weightController.errorMessage ?? 'An error occurred',
                          style: Theme.of(context).textTheme.titleMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => _weightController.refresh(),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                return TabBarView(
                  controller: _tabController,
                  children: [
                    WeightAnalyticsTab(controller: _weightController),
                    WeightRecordsTab(controller: _weightController),
                    WeightInsightsTab(controller: _weightController),
                  ],
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          return _buildFloatingActionButton() ?? const SizedBox.shrink();
        },
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // Records tab
        return FabStyles.add(
          onPressed: _addWeightRecord,
          tooltip: 'Add Weight Record',
        );
      default:
        return null;
    }
  }

  void _addWeightRecord() {
    WeightFormDialog.show(
      context: context,
      cattle: _weightController.allCattle,
      onRecordAdded: () => _weightController.refresh(),
    );
  }
}