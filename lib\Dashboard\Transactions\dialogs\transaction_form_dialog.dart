import 'package:flutter/material.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../../widgets/dialogs/index.dart';

/// Ultra-clean facade using centralized ModuleFormConfigs
class TransactionFormDialog {
  static Future<TransactionIsar?> show({
    required BuildContext context,
    required Function(TransactionIsar) onSave,
    required List<CategoryIsar> categories,
    TransactionIsar? transaction,
  }) async {
    const formConfigs = ModuleFormConfigs();

    return await UniversalFormDialog.show<TransactionIsar>(
      context: context,
      config: FormConfig.simple(
        title: transaction == null ? 'Add Transaction' : 'Edit Transaction',
        fields: formConfigs.forTransaction(),
      ),
      initialValues: transaction != null ? {
        FormKeys.type.value: transaction.categoryType,
        FormKeys.categoryId.value: transaction.category,
        FormKeys.amount.value: transaction.amount,
        FormKeys.date.value: transaction.date,
        FormKeys.description.value: transaction.description,
        FormKeys.notes.value: transaction.description,
      } : {
        FormKeys.type.value: 'Expense',
        FormKeys.date.value: DateTime.now(),
      },
      section: 'transactions',
      closeOnSave: true,
      onSave: (values) async {
        try {
          final transactionRecord = transaction?.copyWith(
            categoryType: values[FormKeys.type.value],
            category: values[FormKeys.categoryId.value],
            amount: values[FormKeys.amount.value],
            date: values[FormKeys.date.value],
            description: values[FormKeys.notes.value],
          ) ?? TransactionIsar.create(
            transactionId: DateTime.now().millisecondsSinceEpoch.toString(),
            title: values[FormKeys.description.value] ?? '',
            amount: values[FormKeys.amount.value] ?? 0.0,
            description: values[FormKeys.description.value] ?? '',
            category: values[FormKeys.categoryId.value] ?? '',
            categoryType: values[FormKeys.type.value] ?? 'Expense',
            date: values[FormKeys.date.value] ?? DateTime.now(),
            icon: Icons.attach_money, // Default icon
          );
          await onSave(transactionRecord);
          return transactionRecord;
        } catch (e) {
          return false;
        }
      },
    );
  }


}
