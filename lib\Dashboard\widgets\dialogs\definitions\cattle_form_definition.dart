import '../../../Cattle/models/cattle_isar.dart';
import '../../../Farm Setup/models/animal_type_isar.dart';
import '../form_config.dart';
import '../form_keys.dart';
import '../form_enums.dart';
import '../config/module_form_configs.dart';
import 'dart:math' as math;

/// Ultimate form architecture: Complete business logic extraction for cattle form
/// This definition class handles all cattle-specific logic including:
/// - Tag ID auto-generation with animal type prefixes
/// - Dependent field updates (breed based on animal type)
/// - Conditional validation (source-specific requirements)
/// - Complex save logic with proper data mapping
class CattleFormDefinition {
  final List<AnimalTypeIsar> animalTypes;
  final List<CattleIsar> existingCattle;
  final String businessId;
  final CattleIsar? existingRecord;

  const CattleFormDefinition({
    required this.animalTypes,
    required this.existingCattle,
    required this.businessId,
    this.existingRecord,
  });

  /// Get form configuration with all fields and validation rules
  FormConfig getFormConfig() {
    const formConfigs = ModuleFormConfigs();
    
    return FormConfig.simple(
      title: existingRecord == null ? 'Add Cattle' : 'Edit Cattle',
      fields: formConfigs.forCattle(),
    );
  }

  /// Get initial values for the form
  Map<String, dynamic> getInitialValues() {
    if (existingRecord == null) {
      return {
        FormKeys.autoGenerateTagId.value: true,
        FormKeys.source.value: CattleSource.purchased.value,
      };
    }

    final cattle = existingRecord!;
    return {
      FormKeys.tagId.value: cattle.tagId ?? '',
      FormKeys.name.value: cattle.name ?? '',
      FormKeys.animalType.value: cattle.animalTypeId ?? '',
      FormKeys.gender.value: cattle.gender ?? '',
      FormKeys.breed.value: cattle.breedId ?? '',
      FormKeys.source.value: cattle.source ?? CattleSource.purchased.value,
      FormKeys.dateOfBirth.value: cattle.dateOfBirth,
      FormKeys.motherTagId.value: cattle.motherTagId ?? '',
      FormKeys.purchaseDate.value: cattle.purchaseDate,
      FormKeys.purchasePrice.value: cattle.purchasePrice ?? 0.0,
      FormKeys.weight.value: cattle.weight ?? 0.0,
      FormKeys.color.value: cattle.color ?? '',
      FormKeys.notes.value: cattle.notes ?? '',
      FormKeys.autoGenerateTagId.value: false, // Disable auto-gen when editing
    };
  }

  /// Handle form value changes and dependencies
  Map<String, dynamic> handleFieldChange(
    String fieldKey,
    dynamic value,
    Map<String, dynamic> currentValues,
  ) {
    final updatedValues = Map<String, dynamic>.from(currentValues);
    updatedValues[fieldKey] = value;

    // Handle auto-generate tag ID toggle
    if (fieldKey == FormKeys.autoGenerateTagId.value) {
      if (value == true) {
        // Generate new tag ID when auto-generate is enabled
        final tagId = _generateTagId(updatedValues[FormKeys.animalType.value]);
        updatedValues[FormKeys.tagId.value] = tagId;
      }
    }

    // Handle animal type change - clear breed and regenerate tag ID
    if (fieldKey == FormKeys.animalType.value) {
      updatedValues[FormKeys.breed.value] = null;
      
      // Auto-generate tag ID if enabled
      if (updatedValues[FormKeys.autoGenerateTagId.value] == true) {
        final tagId = _generateTagId(value);
        updatedValues[FormKeys.tagId.value] = tagId;
      }
    }

    // Handle source change - clear conditional fields
    if (fieldKey == FormKeys.source.value) {
      if (value != CattleSource.bornAtFarm.value) {
        updatedValues[FormKeys.dateOfBirth.value] = null;
        updatedValues[FormKeys.motherTagId.value] = null;
      }
      if (value != CattleSource.purchased.value) {
        updatedValues[FormKeys.purchaseDate.value] = null;
        updatedValues[FormKeys.purchasePrice.value] = null;
      }
    }

    return updatedValues;
  }

  /// Validate form values with source-specific rules
  List<String> validateForm(Map<String, dynamic> values) {
    final errors = <String>[];

    // Basic required field validation
    if (_isEmpty(values[FormKeys.name.value])) {
      errors.add('Name is required');
    }
    if (_isEmpty(values[FormKeys.animalType.value])) {
      errors.add('Animal type is required');
    }
    if (_isEmpty(values[FormKeys.gender.value])) {
      errors.add('Gender is required');
    }
    if (_isEmpty(values[FormKeys.breed.value])) {
      errors.add('Breed is required');
    }
    if (_isEmpty(values[FormKeys.source.value])) {
      errors.add('Source is required');
    }

    // Tag ID validation
    final tagId = values[FormKeys.tagId.value]?.toString().trim() ?? '';
    if (tagId.isEmpty) {
      errors.add('Tag ID is required');
    } else {
      // Check format
      if (!_isValidTagIdFormat(tagId, values[FormKeys.animalType.value])) {
        errors.add('Tag ID must start with animal type first letter followed by numbers');
      }
      // Check uniqueness
      if (_isTagIdInUse(tagId)) {
        errors.add('This tag ID is already in use by another animal');
      }
    }

    // Source-specific validation
    final source = values[FormKeys.source.value];
    if (source == CattleSource.bornAtFarm.value) {
      if (values[FormKeys.dateOfBirth.value] == null) {
        errors.add('Date of birth is required for animals born at farm');
      }
      if (_isEmpty(values[FormKeys.motherTagId.value])) {
        errors.add('Mother tag ID is required for animals born at farm');
      }
    }

    if (source == CattleSource.purchased.value) {
      if (values[FormKeys.purchaseDate.value] == null) {
        errors.add('Purchase date is required for purchased animals');
      }
      final purchasePrice = values[FormKeys.purchasePrice.value];
      if (purchasePrice == null || purchasePrice <= 0) {
        errors.add('Purchase price is required for purchased animals');
      }
    }

    return errors;
  }

  /// Save the cattle record
  Future<CattleIsar> saveRecord(Map<String, dynamic> values) async {
    // Generate tag ID if auto-generate is enabled and field is empty
    if (values[FormKeys.autoGenerateTagId.value] == true) {
      final currentTagId = values[FormKeys.tagId.value]?.toString().trim() ?? '';
      if (currentTagId.isEmpty) {
        values[FormKeys.tagId.value] = _generateTagId(values[FormKeys.animalType.value]);
      }
    }

    // Create or update cattle record
    final cattle = existingRecord != null ? _updateExistingCattle(values) : _createNewCattle(values);
    
    return cattle;
  }

  /// Create new cattle record from form values
  CattleIsar _createNewCattle(Map<String, dynamic> values) {
    final cattle = CattleIsar()
      ..businessId = businessId
      ..tagId = values[FormKeys.tagId.value]?.toString().trim()
      ..name = values[FormKeys.name.value]?.toString().trim()
      ..animalTypeId = values[FormKeys.animalType.value]?.toString()
      ..breedId = values[FormKeys.breed.value]?.toString()
      ..gender = values[FormKeys.gender.value]?.toString()
      ..source = values[FormKeys.source.value]?.toString()
      ..dateOfBirth = values[FormKeys.dateOfBirth.value] as DateTime?
      ..motherTagId = values[FormKeys.motherTagId.value]?.toString()
      ..purchaseDate = values[FormKeys.purchaseDate.value] as DateTime?
      ..purchasePrice = values[FormKeys.purchasePrice.value] as double?
      ..weight = values[FormKeys.weight.value] as double?
      ..color = values[FormKeys.color.value]?.toString().trim()
      ..notes = values[FormKeys.notes.value]?.toString().trim()
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now()
      ..status = 'Active';

    // Initialize empty objects to avoid null errors
    // These will be initialized by the service layer

    return cattle;
  }

  /// Update existing cattle record with form values
  CattleIsar _updateExistingCattle(Map<String, dynamic> values) {
    final cattle = existingRecord!;
    
    return cattle.copyWith(
      tagId: values[FormKeys.tagId.value]?.toString().trim(),
      name: values[FormKeys.name.value]?.toString().trim(),
      animalTypeId: values[FormKeys.animalType.value]?.toString(),
      breedId: values[FormKeys.breed.value]?.toString(),
      gender: values[FormKeys.gender.value]?.toString(),
      source: values[FormKeys.source.value]?.toString(),
      dateOfBirth: values[FormKeys.dateOfBirth.value] as DateTime?,
      motherTagId: values[FormKeys.motherTagId.value]?.toString(),
      purchaseDate: values[FormKeys.purchaseDate.value] as DateTime?,
      purchasePrice: values[FormKeys.purchasePrice.value] as double?,
      weight: values[FormKeys.weight.value] as double?,
      color: values[FormKeys.color.value]?.toString().trim(),
      notes: values[FormKeys.notes.value]?.toString().trim(),
    )..updatedAt = DateTime.now();
  }

  /// Generate tag ID based on animal type prefix
  String _generateTagId(String? animalTypeId) {
    if (animalTypeId == null || animalTypeId.isEmpty) return '';

    final animalType = animalTypes.firstWhere(
      (type) => type.businessId == animalTypeId,
      orElse: () => AnimalTypeIsar(),
    );
    final prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'X';

    // Find max number among existing tags with this prefix
    int maxNumber = 0;
    final regExp = RegExp('^$prefix(\\d+)\$');

    for (final cattle in existingCattle) {
      final tagId = cattle.tagId;
      if (tagId != null && regExp.hasMatch(tagId)) {
        final match = regExp.firstMatch(tagId);
        if (match != null && match.groupCount >= 1) {
          final number = int.tryParse(match.group(1) ?? '0') ?? 0;
          maxNumber = math.max(maxNumber, number);
        }
      }
    }

    return '$prefix${maxNumber + 1}';
  }

  /// Validate tag ID format
  bool _isValidTagIdFormat(String tagId, String? animalTypeId) {
    if (animalTypeId == null || animalTypeId.isEmpty) return false;

    final animalType = animalTypes.firstWhere(
      (type) => type.businessId == animalTypeId,
      orElse: () => AnimalTypeIsar(),
    );
    final prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'X';

    final RegExp tagIdRegex = RegExp('^$prefix\\d+\$');
    return tagIdRegex.hasMatch(tagId);
  }

  /// Check if tag ID is already in use
  bool _isTagIdInUse(String tagId) {
    return existingCattle.any((c) =>
        c.tagId == tagId &&
        (existingRecord == null || c.businessId != existingRecord!.businessId));
  }

  /// Helper to check if value is empty
  bool _isEmpty(dynamic value) {
    return value == null || (value is String && value.trim().isEmpty);
  }
}
