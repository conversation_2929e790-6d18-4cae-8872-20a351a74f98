import 'package:flutter/material.dart';
import '../models/breeding_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../services/database/database_helper.dart';
import '../../widgets/dialogs/index.dart';

/// Ultra-clean facade using centralized ModuleFormConfigs
class BreedingFormDialog {
  static Future<BreedingRecordIsar?> show({
    required BuildContext context,
    BreedingRecordIsar? record,
    String? initialCattleId,
    List<CattleIsar>? preloadedCattle,
    List<Map<String, dynamic>>? preloadedAnimalTypes,
  }) async {
    const formConfigs = ModuleFormConfigs();

    return await UniversalFormDialog.show<BreedingRecordIsar>(
      context: context,
      config: FormConfig.simple(
        title: record == null ? 'Add Breeding Record' : 'Edit Breeding Record',
        fields: formConfigs.forBreedingRecord(),
      ),
      initialValues: record != null ? {
        FormKeys.cattleId.value: record.cattleId,
        FormKeys.bullId.value: record.bullIdOrType,
        FormKeys.date.value: record.date ?? DateTime.now(),
        FormKeys.method.value: record.method ?? 'AI',
        FormKeys.status.value: record.status ?? 'Pending',
        FormKeys.expectedDate.value: record.expectedDate,
        FormKeys.notes.value: record.notes ?? '',
      } : {
        FormKeys.cattleId.value: initialCattleId,
        FormKeys.date.value: DateTime.now(),
        FormKeys.method.value: 'AI',
        FormKeys.status.value: 'Pending',
      },
      section: 'breeding',
      closeOnSave: true,
      onSave: (values) async {
        try {
          final breedingRecord = record?.copyWith(
            cattleId: values[FormKeys.cattleId.value],
            bullIdOrType: values[FormKeys.bullId.value] ?? '',
            date: values[FormKeys.date.value],
            method: values[FormKeys.method.value],
            status: values[FormKeys.status.value],
            expectedDate: values[FormKeys.expectedDate.value],
            notes: values[FormKeys.notes.value],
          ) ?? BreedingRecordIsar.create(
            cattleId: values[FormKeys.cattleId.value] ?? '',
            bullIdOrType: values[FormKeys.bullId.value] ?? '',
            date: values[FormKeys.date.value] ?? DateTime.now(),
            method: values[FormKeys.method.value] ?? 'AI',
            status: values[FormKeys.status.value] ?? 'Pending',
            expectedDate: values[FormKeys.expectedDate.value],
            notes: values[FormKeys.notes.value] ?? '',
          );

          if (record != null) {
            await DatabaseHelper.instance.breedingHandler.updateBreedingRecord(breedingRecord);
          } else {
            await DatabaseHelper.instance.breedingHandler.addBreedingRecord(breedingRecord);
          }
          return breedingRecord;
        } catch (e) {
          // Handle error appropriately
          return false;
        }
      },
    );
  }
}
