import 'package:flutter/material.dart';
import '../models/weight_record_isar.dart';
import '../services/weight_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/message_utils.dart';
import '../../widgets/dialogs/index.dart';

/// Ultra-clean facade using centralized ModuleFormConfigs
class WeightFormDialog {
  static Future<void> show({
    required BuildContext context,
    required List<CattleIsar> cattle,
    WeightRecordIsar? existingRecord,
    CattleIsar? preSelectedCattle,
    VoidCallback? onRecordAdded,
  }) async {
    const formConfigs = ModuleFormConfigs();

    await UniversalFormDialog.show(
      context: context,
      config: FormConfig.simple(
        title: 'Weight Record',
        subtitle: 'Record a new weight measurement',
        fields: formConfigs.forWeight(),
      ),
      initialValues: existingRecord != null ? {
        FormKeys.cattle.value: existingRecord.cattleBusinessId,
        FormKeys.weight.value: existingRecord.weight,
        FormKeys.weightUnit.value: existingRecord.weightUnit,
        FormKeys.measurementDate.value: existingRecord.measurementDate,
        FormKeys.measurementMethod.value: existingRecord.measurementMethod,
        FormKeys.measurementQuality.value: existingRecord.measurementQuality,
        FormKeys.healthStatus.value: existingRecord.healthStatus,
        FormKeys.feedingStatus.value: existingRecord.feedingStatus,
        FormKeys.bodyConditionScore.value: existingRecord.bodyConditionScore,
        FormKeys.isPregnant.value: existingRecord.isPregnant,
        FormKeys.pregnancyStage.value: existingRecord.pregnancyStage,
        FormKeys.season.value: existingRecord.season,
        FormKeys.feedQuality.value: existingRecord.feedQuality,
        FormKeys.isEstimate.value: existingRecord.isEstimate,
        FormKeys.confidenceLevel.value: existingRecord.confidenceLevel,
        FormKeys.notes.value: existingRecord.notes,
      } : {
        FormKeys.cattle.value: preSelectedCattle?.businessId,
        FormKeys.measurementDate.value: DateTime.now(),
        FormKeys.weightUnit.value: WeightUnit.kg.value,
        FormKeys.measurementMethod.value: MeasurementMethod.scale.value,
        FormKeys.measurementQuality.value: MeasurementQuality.good.value,
        FormKeys.healthStatus.value: HealthStatus.healthy.value,
        FormKeys.feedingStatus.value: FeedingStatus.normal.value,
      },
      section: 'weight',
      onSave: (formValues) async {
        try {
          final weightService = WeightService();
          final record = WeightRecordIsar.fromFormValues(formValues);

          if (existingRecord != null) {
            record.id = existingRecord.id;
            record.businessId = existingRecord.businessId;
            record.createdAt = existingRecord.createdAt;
            await weightService.updateWeightRecord(record);
            if (context.mounted) {
              MessageUtils.showSuccess(context, 'Weight record updated successfully');
            }
          } else {
            await weightService.addWeightRecord(record);
            if (context.mounted) {
              MessageUtils.showSuccess(context, 'Weight record added successfully');
            }
          }

          onRecordAdded?.call();
          return true;
        } catch (e) {
          if (context.mounted) {
            MessageUtils.showError(context, 'Error saving weight record: $e');
          }
          return false;
        }
      },
    );
  }
}