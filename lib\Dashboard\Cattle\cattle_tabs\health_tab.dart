import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
// Import Uuid
import '../models/cattle_isar.dart';
import '../../Health/models/health_record_isar.dart';
import '../../Health/models/medication_isar.dart';
import '../../Health/models/treatment_isar.dart';
import '../../Health/models/vaccination_record_isar.dart';
import '../../Health/services/health_handler.dart';
import '../../../Dashboard/Health/dialogs/health_record_form_dialog.dart';
import '../../../Dashboard/Health/dialogs/vaccination_form_dialog.dart';
import '../../../Dashboard/Health/dialogs/treatment_form_dialog.dart';
import '../../../utils/message_utils.dart';
import 'package:intl/intl.dart';

class HealthTab extends StatefulWidget {
  final CattleIsar cattle;
  final int initialTabIndex;
  final HealthHandler? healthHandler; // Optional to maintain backward compatibility

  const HealthTab({
    Key? key,
    required this.cattle,
    this.initialTabIndex = 0,
    this.healthHandler,
  }) : super(key: key);

  @override
  State<HealthTab> createState() => _HealthTabState();
}

class _HealthTabState extends State<HealthTab>
    with SingleTickerProviderStateMixin {
  // Define primary green color as a class constant for consistent use
  static const Color kPrimaryGreen = Color(0xFF2E7D32);
  late HealthHandler _healthHandler;
  List<HealthRecordIsar> _healthRecords = [];
  List<MedicationIsar> _treatments = [];
  List<VaccinationIsar> _vaccinations = [];
  late TabController _tabController; // Made non-nullable
  bool _isLoading = true;
  bool _isMounted = false;
  String? _initializationError; // To store initialization error message

  @override
  void initState() {
    super.initState();
    _isMounted = true;

    // Initialize TabController synchronously
    _tabController = TabController(
      length: 3,
      vsync: this,
      initialIndex: widget.initialTabIndex,
    );
    _tabController.addListener(_handleTabChange); // Add listener for FAB state

    // Initialize HealthHandler and load data
    _initializeAndLoadData();
  }

  Future<void> _initializeAndLoadData() async {
    try {
      // Use injected handler if provided, otherwise try to get from GetIt
      if (widget.healthHandler != null) {
        _healthHandler = widget.healthHandler!;
      } else {
        _healthHandler = GetIt.instance<HealthHandler>();
      }
      await _loadData(); // Load initial data
    } catch (e, s) {
      debugPrint('FATAL: Error getting HealthHandler via GetIt: $e\n$s');
      if (_isMounted) {
        setState(() {
          _initializationError =
              'Failed to initialize health services: ${_getReadableErrorMessage(e)}';
          _isLoading = false;
        });
      }
    }
  }

  void _handleTabChange() {
    // Update the FAB visibility/type when the tab changes
    if (_isMounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _isMounted = false;
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  // Safely update state only if widget is mounted
  void _safeSetState(VoidCallback fn) {
    if (_isMounted) {
      setState(fn);
    }
  }

  // Show a user-friendly SnackBar - Deprecated, use HealthMessageUtils instead
  void _showSnackBar(String message, {bool isError = false}) {
    if (!_isMounted || !context.mounted) return; // Check both mounted states

    if (isError) {
      HealthMessageUtils.showError(context, message);
    } else {
      HealthMessageUtils.showSuccess(context, message);
    }
  }

  Future<void> _loadData() async {
    // Check if businessId is valid - show error and stop if not
    final businessId = widget.cattle.businessId;
    if (businessId == null || businessId.isEmpty) {
      debugPrint('Error: Cattle businessId is null or empty in HealthTab.');
      if (_isMounted) {
        setState(() {
          _isLoading = false;
          _initializationError = 'Cannot load health data: Invalid Cattle ID.';
        });
      }
      return;
    }

    if (!_isMounted) return;
    // Set loading state only if not already showing an initialization error
    if (_initializationError == null) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      // Use the validated businessId
      final healthRecordsFuture =
          _healthHandler.getHealthRecordsForCattle(businessId);
      final treatmentsFuture =
          _healthHandler.getMedicationsForCattle(businessId);
      final vaccinationsFuture =
          _healthHandler.getVaccinationsForCattle(businessId);

      // Await all futures concurrently
      final results = await Future.wait(
          [healthRecordsFuture, treatmentsFuture, vaccinationsFuture]);

      final healthRecords = results[0] as List<HealthRecordIsar>;
      final treatments = results[1] as List<MedicationIsar>;
      final vaccinations = results[2] as List<VaccinationIsar>;

      if (!_isMounted) return;

      _safeSetState(() {
        // Sort health records by date descending
        _healthRecords = healthRecords
          ..sort((a, b) =>
              (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));

        // Sort treatments by start date descending
        _treatments = treatments
          ..sort((a, b) => (b.startDate ?? DateTime(0))
              .compareTo(a.startDate ?? DateTime(0)));

        // Sort vaccinations by date descending
        _vaccinations = vaccinations
          ..sort((a, b) =>
              (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));

        _isLoading = false;
        _initializationError =
            null; // Clear any previous error on successful load
      });
    } catch (e, s) {
      debugPrint('Error loading health data: $e\n$s');
      if (_isMounted) {
        _safeSetState(() {
          _isLoading = false;
          // Optionally clear lists or keep stale data
          _healthRecords = [];
          _treatments = [];
          _vaccinations = [];
        });
        // Show snackbar feedback for loading error
        _showSnackBar('Error loading health data. Please try again.',
            isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context); // Get theme

    // Handle Initialization Error
    if (_initializationError != null) {
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline,
                    size: 48, color: theme.colorScheme.error),
                const SizedBox(height: 16),
                Text(
                  _initializationError!,
                  style: theme.textTheme.titleMedium
                      ?.copyWith(color: theme.colorScheme.error),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                // Optional: Add a retry button if applicable
                // ElevatedButton(
                //   onPressed: _initializeAndLoadData,
                //   child: const Text('Try Again'),
                // ),
              ],
            ),
          ),
        ),
      );
    }

    // Handle Loading State
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: theme.colorScheme.surface,
          elevation: 0,
          toolbarHeight: 0,
          bottom: _buildTabBar(theme), // Build tab bar even while loading
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Main Content
    return Scaffold(
      appBar: AppBar(
        // backgroundColor: Colors.white, // Use theme
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        toolbarHeight: 0,
        bottom: _buildTabBar(theme),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Health Records Tab - Removed SingleChildScrollView
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: RefreshIndicator(
              onRefresh: _loadData,
              child: ListView(
                // Use ListView instead of Column for RefreshIndicator
                children: [
                  _buildCurrentStatusCard(theme),
                  const SizedBox(height: 16),
                  _buildHealthRecordsCard(theme),
                ],
              ),
            ),
          ),
          // Treatments Tab - Removed SingleChildScrollView
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: RefreshIndicator(
              onRefresh: _loadData,
              child: _buildTreatmentsCard(theme), // ListView is inside the card
            ),
          ),
          // Vaccinations Tab - Removed SingleChildScrollView
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: RefreshIndicator(
              onRefresh: _loadData,
              child:
                  _buildVaccinationsCard(theme), // ListView is inside the card
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFAB(theme),
    );
  }

  // Helper to build TabBar for reuse
  TabBar _buildTabBar(ThemeData theme) {
    // Use class constant for primary green color
    const Color treatmentBlue = Colors.blue;
    const Color vaccinationPurple = Colors.purple;

    return TabBar(
      controller: _tabController,
      // No global label color - we'll set it per tab
      labelColor: null,
      unselectedLabelColor: null,
      indicatorColor: kPrimaryGreen,
      tabs: [
        Tab(
          icon: Icon(
            Icons.medical_services_rounded,
            color: _tabController.index == 0
                ? kPrimaryGreen // Active: Bright green
                : kPrimaryGreen.withAlpha(179), // Inactive: Light green (0.7 * 255 = 179)
          ),
          child: Text(
            'Health',
            style: TextStyle(
              color: _tabController.index == 0
                  ? kPrimaryGreen // Active: Bright green
                  : kPrimaryGreen.withAlpha(179), // Inactive: Light green (0.7 * 255 = 179)
              fontWeight: _tabController.index == 0 ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Tab(
          icon: Icon(
            Icons.medication_rounded,
            color: _tabController.index == 1
                ? treatmentBlue // Active: Bright blue
                : treatmentBlue.withAlpha(128), // Inactive: Light blue (0.5 * 255 = 128)
          ),
          child: Text(
            'Treatments',
            style: TextStyle(
              color: _tabController.index == 1
                  ? treatmentBlue // Active: Bright blue
                  : treatmentBlue.withAlpha(128), // Inactive: Light blue (0.5 * 255 = 128)
              fontWeight: _tabController.index == 1 ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Tab(
          icon: Icon(
            Icons.vaccines_rounded,
            color: _tabController.index == 2
                ? vaccinationPurple // Active: Bright purple
                : vaccinationPurple.withAlpha(128), // Inactive: Light purple (0.5 * 255 = 128)
          ),
          child: Text(
            'Vaccinations',
            style: TextStyle(
              color: _tabController.index == 2
                  ? vaccinationPurple // Active: Bright purple
                  : vaccinationPurple.withAlpha(128), // Inactive: Light purple (0.5 * 255 = 128)
              fontWeight: _tabController.index == 2 ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFAB(ThemeData theme) {
    // Use class constant for primary green color
    VoidCallback? onPressed;

    // Use different icons based on the tab
    IconData icon;
    String tooltip;

    switch (_tabController.index) {
      case 0: // Health Records
        onPressed = _addHealthRecord;
        icon = Icons.medical_services_rounded;
        tooltip = 'Add Health Record';
        break;
      case 1: // Treatments
        onPressed = _addTreatment;
        icon = Icons.medication_rounded;
        tooltip = 'Add Treatment';
        break;
      case 2: // Vaccinations
        onPressed = _addVaccination;
        icon = Icons.vaccines_rounded;
        tooltip = 'Add Vaccination';
        break;
      default:
        icon = Icons.add;
        tooltip = 'Add';
    }

    return FloatingActionButton(
      onPressed: onPressed,
      backgroundColor: kPrimaryGreen,
      foregroundColor: Colors.white,
      tooltip: tooltip,
      elevation: 4.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Icon(icon),
    );
  }



  // --- Card Building Methods ---

  // Updated to use theme and simpler logic for latest record
  Widget _buildCurrentStatusCard(ThemeData theme) {
    // Use class constant for primary green color

    // If no health records, show default status with improved empty state
    if (_healthRecords.isEmpty) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with gradient background
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    kPrimaryGreen.withAlpha(179), // 0.7 * 255 = 179
                    kPrimaryGreen.withAlpha(128), // 0.5 * 255 = 128
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.health_and_safety_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Current Health Status',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            // Empty state with better visuals
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: kPrimaryGreen.withAlpha(26), // 0.1 * 255 = 26
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.health_and_safety_outlined,
                        size: 48,
                        color: kPrimaryGreen,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Health Records',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add a health record to start tracking',
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.onSurface.withAlpha(153), // 0.6 * 255 = 153
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Get the latest record (list is sorted descending)
    final latestRecord = _healthRecords.first;
    final statusColor = _getHealthRecordColor(latestRecord.condition, theme);

    // Use the same primary green color defined earlier

    // Define colors for different status types
    const Color treatmentBlue = Colors.blue;
    const Color vetPurple = Colors.purple;
    const Color dateGreen = Color(0xFF2E7D32); // Primary green
    const Color costIndigo = Color(0xFF3F51B5); // Indigo instead of purple

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with gradient background based on health status
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  statusColor,
                  statusColor.withAlpha(179), // 0.7 * 255 = 179
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.health_and_safety_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Health Status',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        latestRecord.condition ?? 'Unknown',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    latestRecord.date != null
                        ? DateFormat('MMM dd').format(latestRecord.date!)
                        : 'Unknown',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Status details with improved layout
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Treatment row
                _buildStatusRow(
                  Icons.healing_rounded,
                  'Treatment',
                  latestRecord.treatment ?? 'None',
                  treatmentBlue,
                  theme,
                ),
                const SizedBox(height: 12),
                // Veterinarian row
                _buildStatusRow(
                  Icons.person_rounded,
                  'Veterinarian',
                  latestRecord.veterinarian ?? 'Not specified',
                  vetPurple,
                  theme,
                ),
                const SizedBox(height: 12),
                // Last checkup row
                _buildStatusRow(
                  Icons.calendar_today_rounded,
                  'Last Checkup',
                  latestRecord.date != null
                      ? DateFormat('MMMM dd, yyyy').format(latestRecord.date!)
                      : 'Unknown date',
                  dateGreen,
                  theme,
                ),
                const SizedBox(height: 12),
                // Cost row
                _buildStatusRow(
                  Icons.attach_money_rounded,
                  'Cost',
                  latestRecord.cost != null
                      ? '\$${latestRecord.cost!.toStringAsFixed(2)}'
                      : '\$0.00',
                  costIndigo,
                  theme,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(IconData icon, String label, String value, Color color, ThemeData theme) {
    return Row(
      children: [
        // Colored icon with background
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(26), // 0.1 * 255 = 26
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 18,
            color: color,
          ),
        ),
        const SizedBox(width: 12),
        // Label and value in a column for better layout
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  color: theme.colorScheme.onSurface.withAlpha(153), // 0.6 * 255 = 153
                ),
              ),
              const SizedBox(height: 2),
              // Value with more prominence
              Text(
                value,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurface,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHealthRecordsCard(ThemeData theme) {
    // Use class constant for primary green color

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with gradient background
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  kPrimaryGreen,
                  kPrimaryGreen.withAlpha(179), // 0.7 * 255 = 179
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.medical_services_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Health Records',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                // Record count badge
                if (_healthRecords.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${_healthRecords.length}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Empty state with better visuals
          if (_healthRecords.isEmpty)
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: kPrimaryGreen.withAlpha(26), // 0.1 * 255 = 26
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.medical_services_outlined,
                        size: 48,
                        color: kPrimaryGreen,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Health Records',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add a health record to start tracking',
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.onSurface.withAlpha(153), // 0.6 * 255 = 153
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            // List of health records
            ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.all(12),
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _healthRecords.length,
              itemBuilder: (context, index) {
                final record = _healthRecords[index];
                return _buildHealthRecordItem(record, theme);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildTreatmentsCard(ThemeData theme) {
    // Define treatment blue color
    const Color treatmentBlue = Colors.blue;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with gradient background
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  treatmentBlue,
                  treatmentBlue.withAlpha(179), // 0.7 * 255 = 179
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.medication_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Treatments',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                // Treatment count badge
                if (_treatments.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${_treatments.length}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Empty state with better visuals
          if (_treatments.isEmpty)
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: treatmentBlue.withAlpha(26), // 0.1 * 255 = 26
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.medication_outlined,
                        size: 48,
                        color: treatmentBlue,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Treatments',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add a treatment to start tracking',
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.onSurface.withAlpha(153), // 0.6 * 255 = 153
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            // List of treatments
            ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.all(12),
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _treatments.length,
              itemBuilder: (context, index) {
                final treatment = _treatments[index];
                return _buildTreatmentItem(treatment, theme);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildVaccinationsCard(ThemeData theme) {
    // Define vaccination purple color
    const Color vaccinationPurple = Colors.purple;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with gradient background
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  vaccinationPurple,
                  vaccinationPurple.withAlpha(179), // 0.7 * 255 = 179
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.vaccines_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Vaccinations',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                // Vaccination count badge
                if (_vaccinations.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${_vaccinations.length}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Empty state with better visuals
          if (_vaccinations.isEmpty)
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: vaccinationPurple.withAlpha(26), // 0.1 * 255 = 26
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.vaccines_outlined,
                        size: 48,
                        color: vaccinationPurple,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Vaccinations',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add a vaccination to start tracking',
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.onSurface.withAlpha(153), // 0.6 * 255 = 153
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            // List of vaccinations
            ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.all(12),
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _vaccinations.length,
              itemBuilder: (context, index) {
                final vaccination = _vaccinations[index];
                return _buildVaccinationItem(vaccination, theme);
              },
            ),
        ],
      ),
    );
  }

  // --- List Item Building Methods ---

  Widget _buildHealthRecordItem(HealthRecordIsar record, ThemeData theme) {
    final recordColor = _getHealthRecordColor(record.condition, theme);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: recordColor.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: recordColor.withAlpha(50),
                        child: Icon(
                          Icons.medical_services,
                          color: recordColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              record.date != null
                                  ? DateFormat('MMMM dd, yyyy').format(record.date!)
                                  : 'Unknown date',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              record.condition ?? 'Unknown',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: recordColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  tooltip: 'More options',
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(
                            Icons.delete,
                            color: Colors.red,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Delete',
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) async {
                    switch (value) {
                      case 'edit':
                        final updatedRecord = await HealthRecordFormDialog.show(
                          context: context,
                          cattle: [widget.cattle],
                          healthRecord: record,
                        );

                        if (!_isMounted || updatedRecord == null) return;

                        try {
                          // Save the updated record
                          final savedRecord = await _healthHandler.addOrUpdateHealthRecord(updatedRecord);

                          // Use local state update instead of reloading all records
                          _safeSetState(() {
                            // Find and replace the record in the list
                            final index = _healthRecords.indexWhere((r) => r.recordId == savedRecord.recordId);
                            if (index >= 0) {
                              _healthRecords[index] = savedRecord;
                            }
                            // Re-sort records to maintain order
                            _healthRecords.sort((a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
                          });

                          _showSnackBar('Health record updated successfully');
                        } catch (e) {
                          _showSnackBar('Failed to update health record: ${_getReadableErrorMessage(e)}', isError: true);
                        }
                        break;
                      case 'delete':
                        _deleteHealthRecord(record);
                        break;
                    }
                  },
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () => _showHealthRecordDetails(record),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildStatusRow(
                    Icons.medical_services_outlined,
                    'Treatment',
                    record.treatment ?? 'None',
                    theme.colorScheme.secondary,
                    theme,
                  ),
                  const SizedBox(height: 8),
                  _buildStatusRow(
                    Icons.person_outline,
                    'Veterinarian',
                    record.veterinarian ?? 'Not specified',
                    theme.colorScheme.tertiary,
                    theme,
                  ),
                  const SizedBox(height: 8),
                  _buildStatusRow(
                    Icons.attach_money_outlined,
                    'Cost',
                    record.cost != null
                        ? '\$${record.cost!.toStringAsFixed(2)}'
                        : '\$0.00',
                    Colors.purple,
                    theme,
                  ),
                  if (record.notes != null && record.notes!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    _buildStatusRow(
                      Icons.notes_outlined,
                      'Notes',
                      record.notes!,
                      Colors.grey,
                      theme,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTreatmentItem(MedicationIsar treatment, ThemeData theme) {
    final startDate = treatment.startDate != null
        ? DateFormat('MMMM dd, yyyy').format(treatment.startDate!)
        : 'No start date';
    final endDate = treatment.endDate != null
        ? DateFormat('MMMM dd, yyyy').format(treatment.endDate!)
        : 'Ongoing';
    final status = treatment.frequency ?? 'Ongoing';
    final statusColor = _getTreatmentStatusColor(status, theme);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: statusColor.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: statusColor.withAlpha(50),
                        child: Icon(
                          Icons.medication,
                          color: statusColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              startDate,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              treatment.name ?? 'Unknown Treatment',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: statusColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  tooltip: 'More options',
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(
                            Icons.delete,
                            color: Colors.red,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Delete',
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) async {
                    switch (value) {
                      case 'edit':
                        final updatedTreatment = await showDialog<TreatmentIsar>(
                          context: context,
                          builder: (_) => TreatmentFormDialog(
                            cattle: [widget.cattle],
                            cattleId: treatment.cattleId,
                            treatment: treatment.toTreatmentIsar(),
                          ),
                        );

                        if (!_isMounted || updatedTreatment == null) return;

                        try {
                          // Convert TreatmentIsar to MedicationIsar and save
                          final medication = _treatmentToMedication(updatedTreatment);
                          medication.businessId = treatment.businessId;
                          final savedMedication = await _healthHandler.addTreatment(medication);

                          // Use local state update instead of reloading all records
                          _safeSetState(() {
                            // Find and replace the treatment in the list
                            final index = _treatments.indexWhere((t) => t.businessId == savedMedication.businessId);
                            if (index >= 0) {
                              _treatments[index] = savedMedication;
                            }
                            // Re-sort treatments to maintain order
                            _treatments.sort((a, b) => (b.startDate ?? DateTime(0)).compareTo(a.startDate ?? DateTime(0)));
                          });

                          _showSnackBar('Treatment updated successfully');
                        } catch (e) {
                          _showSnackBar('Failed to update treatment: ${_getReadableErrorMessage(e)}', isError: true);
                        }
                        break;
                      case 'delete':
                        _deleteTreatment(treatment);
                        break;
                    }
                  },
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () => _showTreatmentDetails(treatment),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildStatusRow(
                    Icons.medical_services_outlined,
                    'Status',
                    status,
                    statusColor,
                    theme,
                  ),
                  const SizedBox(height: 8),
                  _buildStatusRow(
                    Icons.medication_outlined,
                    'Dosage',
                    treatment.dosage ?? 'Not specified',
                    Colors.orange,
                    theme,
                  ),
                  const SizedBox(height: 8),
                  _buildStatusRow(
                    Icons.date_range_outlined,
                    'Period',
                    '$startDate - $endDate',
                    Colors.blue,
                    theme,
                  ),
                  const SizedBox(height: 8),
                  _buildStatusRow(
                    Icons.attach_money_outlined,
                    'Cost',
                    treatment.cost != null
                        ? '\$${treatment.cost!.toStringAsFixed(2)}'
                        : '\$0.00',
                    Colors.purple,
                    theme,
                  ),
                  if (treatment.notes != null && treatment.notes!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    _buildStatusRow(
                      Icons.notes_outlined,
                      'Notes',
                      treatment.notes!,
                      Colors.grey,
                      theme,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getTreatmentStatusColor(String status, ThemeData theme) {
    status = status.toLowerCase();
    if (status.contains('completed')) return Colors.green;
    if (status.contains('ongoing')) return Colors.orange;
    if (status.contains('scheduled')) return theme.colorScheme.secondary;
    if (status.contains('cancelled') || status.contains('stopped')) {
      return Colors.red;
    }
    return Colors.orange; // Default to orange for active/ongoing treatments
  }

  Widget _buildVaccinationItem(VaccinationIsar vaccination, ThemeData theme) {
    final vaccinationDate = vaccination.date != null
        ? DateFormat('MMMM dd, yyyy').format(vaccination.date!)
        : 'No date';
    final nextDueDate = vaccination.nextDueDate != null
        ? DateFormat('MMMM dd, yyyy').format(vaccination.nextDueDate!)
        : null;
    final status = vaccination.status ?? 'Completed';
    final statusColor = _getVaccinationStatusColor(status, theme);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: statusColor.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: statusColor.withAlpha(50),
                        child: Icon(
                          Icons.vaccines,
                          color: statusColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              vaccinationDate,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              vaccination.vaccineName ?? 'Unknown Vaccine',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: statusColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  tooltip: 'More options',
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(
                            Icons.delete,
                            color: Colors.red,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Delete',
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) async {
                    switch (value) {
                      case 'edit':
                        final updatedVaccination = await showDialog<VaccinationIsar>(
                          context: context,
                          builder: (_) => VaccinationFormDialog(
                            cattle: [widget.cattle],
                            cattleId: vaccination.cattleId ?? '',
                            vaccination: vaccination,
                          ),
                        );

                        if (!_isMounted || updatedVaccination == null) return;

                        try {
                          // Save the updated vaccination
                          final savedVaccination = await _healthHandler.addOrUpdateVaccination(
                            updatedVaccination.cattleId ?? '',
                            updatedVaccination
                          );

                          // Use local state update instead of reloading all records
                          _safeSetState(() {
                            // Find and replace the vaccination in the list
                            final index = _vaccinations.indexWhere((v) => v.recordId == savedVaccination.recordId);
                            if (index >= 0) {
                              _vaccinations[index] = savedVaccination;
                            }
                            // Re-sort vaccinations to maintain order
                            _vaccinations.sort((a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
                          });

                          _showSnackBar('Vaccination updated successfully');
                        } catch (e) {
                          _showSnackBar('Failed to update vaccination: ${_getReadableErrorMessage(e)}', isError: true);
                        }
                        break;
                      case 'delete':
                        _deleteVaccination(vaccination);
                        break;
                    }
                  },
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () => _showVaccinationDetails(vaccination),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildStatusRow(
                    Icons.medical_services_outlined,
                    'Status',
                    status,
                    statusColor,
                    theme,
                  ),
                  const SizedBox(height: 8),
                  _buildStatusRow(
                    Icons.confirmation_number_outlined,
                    'Batch',
                    vaccination.batchNumber ?? 'Not specified',
                    Colors.blue,
                    theme,
                  ),
                  if (vaccination.manufacturer != null && vaccination.manufacturer!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    _buildStatusRow(
                      Icons.business_outlined,
                      'Manufacturer',
                      vaccination.manufacturer!,
                      Colors.teal,
                      theme,
                    ),
                  ],
                  if (nextDueDate != null) ...[
                    const SizedBox(height: 8),
                    _buildStatusRow(
                      Icons.event_outlined,
                      'Next Due',
                      nextDueDate,
                      Colors.orange,
                      theme,
                    ),
                  ],
                  const SizedBox(height: 8),
                  _buildStatusRow(
                    Icons.attach_money_outlined,
                    'Cost',
                    vaccination.cost != null
                        ? '\$${vaccination.cost!.toStringAsFixed(2)}'
                        : '\$0.00',
                    Colors.purple,
                    theme,
                  ),
                  if (vaccination.notes != null && vaccination.notes!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    _buildStatusRow(
                      Icons.notes_outlined,
                      'Notes',
                      vaccination.notes!,
                      Colors.grey,
                      theme,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getVaccinationStatusColor(String status, ThemeData theme) {
    final statusLower = status.toLowerCase();
    if (statusLower.contains('administered') ||
        statusLower.contains('completed')) {
      return Colors.green;
    } else if (statusLower.contains('scheduled')) {
      return theme.colorScheme.tertiary;
    } else if (statusLower.contains('missed') ||
        statusLower.contains('expired')) {
      return Colors.red;
    } else if (statusLower.contains('due')) {
      return Colors.orange;
    }
    return theme.colorScheme.tertiary; // Default to tertiary for pending vaccinations
  }

  // Convert error to user-friendly message
  String _getReadableErrorMessage(dynamic error) {
    final message = error.toString();

    // Check for specific error types
    if (message.contains('ValidationException')) {
      // Extract the validation message
      final validationMessage = message.replaceAll('ValidationException: ', '');
      return validationMessage;
    }
    else if (message.contains('RecordNotFoundException')) {
      return 'The record could not be found. It may have been deleted.';
    }
    else if (message.contains('DatabaseException')) {
      return 'A database error occurred. Please try again.';
    }
    else if (message.contains('Cattle ID is required')) {
      return 'Cattle ID is missing. Please select a valid cattle.';
    }
    else if (message.contains('Record date is required')) {
      return 'Please select a valid date for the record.';
    }
    else if (message.contains('Vaccination date is required')) {
      return 'Please select a vaccination date.';
    }
    else if (message.contains('Vaccine name is required')) {
      return 'Please enter a vaccine name.';
    }
    else if (message.contains('Treatment name is required')) {
      return 'Please enter a treatment name.';
    }
    else if (message.contains('Dosage is required')) {
      return 'Please enter a dosage for the treatment.';
    }

    // Generic error message as fallback
    return 'An error occurred. Please check your inputs and try again.';
  }

  // --- Action Methods (Add, Delete) ---

  Future<void> _addHealthRecord() async {
    if (widget.cattle.businessId == null) {
      _showSnackBar('Cannot add record: Cattle ID is missing.', isError: true);
      return;
    }

    // Await the result from the dialog (now expecting HealthRecordIsar)
    final newRecord = await HealthRecordFormDialog.show(
      context: context,
      cattle: [widget.cattle], // Pass current cattle if needed by dialog
    );

    if (!_isMounted || newRecord == null) return;

    try {
      // Save the record using healthHandler
      final savedRecord = await _healthHandler.addOrUpdateHealthRecord(newRecord);

      // Use local state update instead of reloading all records
      _safeSetState(() {
        // Add the new record at the beginning of the list (most recent first)
        _healthRecords.insert(0, savedRecord);
        // Re-sort records to maintain order
        _healthRecords.sort((a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
      });

      _showSnackBar('Health record added successfully');
    } catch (e) {
      _showSnackBar('Failed to save health record: ${_getReadableErrorMessage(e)}', isError: true);
    }
  }

  Future<void> _editHealthRecord(HealthRecordIsar record) async {
    if (!_isMounted || !context.mounted) return;

    final updatedRecord = await HealthRecordFormDialog.show(
      context: context,
      cattle: [widget.cattle],
      healthRecord: record,
    );

    if (!_isMounted || updatedRecord == null) return;

    try {
      // Save the updated record
      final savedRecord = await _healthHandler.addOrUpdateHealthRecord(updatedRecord);

      // Use local state update instead of reloading all records
      _safeSetState(() {
        // Find and replace the record in the list
        final index = _healthRecords.indexWhere((r) => r.recordId == savedRecord.recordId);
        if (index >= 0) {
          _healthRecords[index] = savedRecord;
        }
        // Re-sort records to maintain order
        _healthRecords.sort((a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
      });

      _showSnackBar('Health record updated successfully');
    } catch (e) {
      _showSnackBar('Failed to update health record: ${_getReadableErrorMessage(e)}', isError: true);
    }
  }

  // Convert TreatmentIsar to MedicationIsar
  MedicationIsar _treatmentToMedication(TreatmentIsar treatment) {
    return MedicationIsar.create(
      cattleBusinessId: treatment.cattleId ?? '',
      name: treatment.treatment ?? '',
      dosage: treatment.dosage ?? '',
      frequency: treatment.status ?? '',
      startDate: treatment.date ?? DateTime.now(),
      notes: treatment.notes ?? '',
      cost: double.tryParse(treatment.cost ?? '0') ?? 0.0,
    );
  }

  Future<void> _addTreatment() async {
    if (widget.cattle.businessId == null) {
      _showSnackBar('Cannot add treatment: Cattle ID is missing.',
          isError: true);
      return;
    }

    final treatmentRecord = await showDialog<TreatmentIsar>(
      context: context,
      builder: (context) => TreatmentFormDialog(
        cattle: [widget.cattle],
        cattleId: widget.cattle.businessId,
      ),
    );

    if (!_isMounted || treatmentRecord == null) return;

    try {
      // Convert TreatmentIsar to MedicationIsar and save
      final medication = _treatmentToMedication(treatmentRecord);
      final savedMedication = await _healthHandler.addTreatment(medication);

      // Use local state update instead of reloading all records
      _safeSetState(() {
        // Add the new treatment at the beginning of the list (most recent first)
        _treatments.insert(0, savedMedication);
        // Re-sort treatments to maintain order
        _treatments.sort((a, b) => (b.startDate ?? DateTime(0)).compareTo(a.startDate ?? DateTime(0)));
      });

      _showSnackBar('Treatment added successfully');
    } catch (e) {
      _showSnackBar('Failed to save treatment: ${_getReadableErrorMessage(e)}', isError: true);
    }
  }

  Future<void> _addVaccination() async {
    if (widget.cattle.businessId == null) {
      _showSnackBar('Cannot add vaccination: Cattle ID is missing.',
          isError: true);
      return;
    }

    final vaccinationRecord = await showDialog<VaccinationIsar>(
      context: context,
      builder: (context) => VaccinationFormDialog(
        cattleId: widget.cattle.businessId!,
        cattle: [widget.cattle],
      ),
    );

    if (!_isMounted || vaccinationRecord == null) return;

    try {
      // Save the vaccination using healthHandler
      final savedVaccination = await _healthHandler.addOrUpdateVaccination(
        vaccinationRecord.cattleId ?? '',
        vaccinationRecord
      );

      // Use local state update instead of reloading all records
      _safeSetState(() {
        // Add the new vaccination at the beginning of the list (most recent first)
        _vaccinations.insert(0, savedVaccination);
        // Re-sort vaccinations to maintain order
        _vaccinations.sort((a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
      });

      _showSnackBar('Vaccination added successfully');
    } catch (e) {
      _showSnackBar('Failed to save vaccination: ${_getReadableErrorMessage(e)}', isError: true);
    }
  }

  Future<void> _deleteHealthRecord(HealthRecordIsar record) async {
    final recordIdToDelete = record.recordId; // Use recordId
    if (recordIdToDelete == null || recordIdToDelete.isEmpty) {
      _showSnackBar('Cannot delete: Record ID missing.', isError: true);
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Health Record'),
        content: Text(
            'Are you sure you want to delete this record (${record.condition ?? 'Unknown Condition'})?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error),
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (!_isMounted || confirmed != true) return;

    try {
      await _healthHandler
          .deleteHealthRecord(recordIdToDelete); // Pass String ID
      if (!_isMounted) return;
      // Local state update (still efficient for delete)
      _safeSetState(() {
        _healthRecords.removeWhere((hr) => hr.recordId == recordIdToDelete);
      });
      _showSnackBar('Health record deleted.');
    } catch (e) {
      if (_isMounted) {
        _showSnackBar('Failed to delete health record: $e', isError: true);
      }
    }
    // No _loadData() needed for delete if local update works
  }

  Future<void> _deleteTreatment(MedicationIsar treatment) async {
    final treatmentIdToDelete =
        treatment.businessId; // Use businessId for Medication/Treatment
    if (treatmentIdToDelete == null || treatmentIdToDelete.isEmpty) {
      _showSnackBar('Cannot delete: Treatment ID missing.', isError: true);
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Treatment'),
        content: Text(
            'Are you sure you want to delete this treatment (${treatment.name ?? 'Unknown'})?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error),
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (!_isMounted || confirmed != true) return;

    try {
      await _healthHandler
          .deleteTreatment(treatmentIdToDelete); // Pass String ID
      if (!_isMounted) return;
      // Local state update
      _safeSetState(() {
        _treatments.removeWhere((t) => t.businessId == treatmentIdToDelete);
      });
      _showSnackBar('Treatment deleted.');
    } catch (e) {
      if (_isMounted) {
        _showSnackBar('Failed to delete treatment: $e', isError: true);
      }
    }
    // No _loadData() needed for delete
  }

  Future<void> _deleteVaccination(VaccinationIsar vaccination) async {
    final vaccinationIdToDelete = vaccination.recordId; // Use recordId
    if (vaccinationIdToDelete == null || vaccinationIdToDelete.isEmpty) {
      _showSnackBar('Cannot delete: Vaccination ID missing.', isError: true);
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Vaccination'),
        content: Text(
            'Are you sure you want to delete this vaccination (${vaccination.vaccineName ?? 'Unknown'})?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error),
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (!_isMounted || confirmed != true) return;

    try {
      await _healthHandler
          .deleteVaccination(vaccinationIdToDelete); // Pass String ID
      if (!_isMounted) return;
      // Local state update
      _safeSetState(() {
        _vaccinations.removeWhere((v) => v.recordId == vaccinationIdToDelete);
      });
      _showSnackBar('Vaccination deleted.');
    } catch (e) {
      if (_isMounted) {
        _showSnackBar('Failed to delete vaccination: $e', isError: true);
      }
    }
    // No _loadData() needed for delete
  }

  // Helper to determine color based on health condition
  Color _getHealthRecordColor(String? condition, ThemeData theme) {
    // Accept theme
    switch (condition?.toLowerCase() ?? '') {
      case 'healthy':
        return Colors.green; // Keep specific colors if desired
      case 'sick':
        return Colors.red;
      case 'recovering':
        return Colors.orange;
      case 'under observation':
        return Colors.yellow[700] ?? Colors.yellow; // Ensure non-nullable
      default:
        return theme.colorScheme.primary; // Use theme color as default
    }
  }

  void _showHealthRecordDetails(HealthRecordIsar record) {
    if (!_isMounted || !context.mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(record.condition ?? 'Health Record'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Date', record.date != null
                ? DateFormat('MMMM dd, yyyy').format(record.date!)
                : 'Unknown date'),
              _buildDetailRow('Condition', record.condition ?? 'Unknown'),
              _buildDetailRow('Treatment', record.treatment ?? 'None'),
              _buildDetailRow('Veterinarian', record.veterinarian ?? 'Not specified'),
              _buildDetailRow('Cost', record.cost != null
                ? '\$${record.cost!.toStringAsFixed(2)}'
                : '\$0.00'),
              if (record.notes != null && record.notes!.isNotEmpty)
                _buildDetailRow('Notes', record.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _editHealthRecord(record);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showTreatmentDetails(MedicationIsar treatment) {
    if (!_isMounted || !context.mounted) return;

    final startDate = treatment.startDate != null
        ? DateFormat('MMMM dd, yyyy').format(treatment.startDate!)
        : 'No start date';
    final endDate = treatment.endDate != null
        ? DateFormat('MMMM dd, yyyy').format(treatment.endDate!)
        : 'Ongoing';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(treatment.name ?? 'Treatment Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Start Date', startDate),
              _buildDetailRow('End Date', endDate),
              _buildDetailRow('Status', treatment.frequency ?? 'Ongoing'),
              _buildDetailRow('Dosage', treatment.dosage ?? 'Not specified'),
              _buildDetailRow('Cost', treatment.cost != null
                ? '\$${treatment.cost!.toStringAsFixed(2)}'
                : '\$0.00'),
              if (treatment.notes != null && treatment.notes!.isNotEmpty)
                _buildDetailRow('Notes', treatment.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final updatedTreatment = await showDialog<TreatmentIsar>(
                context: context,
                builder: (_) => TreatmentFormDialog(
                  cattle: [widget.cattle],
                  cattleId: treatment.cattleId,
                  treatment: treatment.toTreatmentIsar(),
                ),
              );

              if (!_isMounted || updatedTreatment == null) return;

              try {
                // Convert TreatmentIsar to MedicationIsar and save
                final medication = _treatmentToMedication(updatedTreatment);
                medication.businessId = treatment.businessId;
                final savedMedication = await _healthHandler.addTreatment(medication);

                // Use local state update instead of reloading all records
                _safeSetState(() {
                  // Find and replace the treatment in the list
                  final index = _treatments.indexWhere((t) => t.businessId == savedMedication.businessId);
                  if (index >= 0) {
                    _treatments[index] = savedMedication;
                  }
                  // Re-sort treatments to maintain order
                  _treatments.sort((a, b) => (b.startDate ?? DateTime(0)).compareTo(a.startDate ?? DateTime(0)));
                });

                _showSnackBar('Treatment updated successfully');
              } catch (e) {
                _showSnackBar('Failed to update treatment: ${_getReadableErrorMessage(e)}', isError: true);
              }
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  void _showVaccinationDetails(VaccinationIsar vaccination) {
    if (!_isMounted || !context.mounted) return;

    final vaccinationDate = vaccination.date != null
        ? DateFormat('MMMM dd, yyyy').format(vaccination.date!)
        : 'No date';
    final nextDueDate = vaccination.nextDueDate != null
        ? DateFormat('MMMM dd, yyyy').format(vaccination.nextDueDate!)
        : 'Not scheduled';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(vaccination.vaccineName ?? 'Vaccination Details'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Date', vaccinationDate),
              _buildDetailRow('Status', vaccination.status ?? 'Completed'),
              _buildDetailRow('Batch Number', vaccination.batchNumber ?? 'Not specified'),
              if (vaccination.manufacturer != null && vaccination.manufacturer!.isNotEmpty)
                _buildDetailRow('Manufacturer', vaccination.manufacturer!),
              _buildDetailRow('Next Due Date', nextDueDate),
              _buildDetailRow('Cost', vaccination.cost != null
                ? '\$${vaccination.cost!.toStringAsFixed(2)}'
                : '\$0.00'),
              if (vaccination.notes != null && vaccination.notes!.isNotEmpty)
                _buildDetailRow('Notes', vaccination.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final updatedVaccination = await showDialog<VaccinationIsar>(
                context: context,
                builder: (_) => VaccinationFormDialog(
                  cattle: [widget.cattle],
                  cattleId: vaccination.cattleId ?? '',
                  vaccination: vaccination,
                ),
              );

              if (!_isMounted || updatedVaccination == null) return;

              try {
                // Save the updated vaccination
                final savedVaccination = await _healthHandler.addOrUpdateVaccination(
                  updatedVaccination.cattleId ?? '',
                  updatedVaccination
                );

                // Use local state update instead of reloading all records
                _safeSetState(() {
                  // Find and replace the vaccination in the list
                  final index = _vaccinations.indexWhere((v) => v.recordId == savedVaccination.recordId);
                  if (index >= 0) {
                    _vaccinations[index] = savedVaccination;
                  }
                  // Re-sort vaccinations to maintain order
                  _vaccinations.sort((a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
                });

                _showSnackBar('Vaccination updated successfully');
              } catch (e) {
                _showSnackBar('Failed to update vaccination: ${_getReadableErrorMessage(e)}', isError: true);
              }
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }
}
