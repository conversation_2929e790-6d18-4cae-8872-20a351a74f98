import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../controllers/cattle_weight_detail_controller.dart';
import '../models/weight_record_isar.dart';
import '../dialogs/weight_form_dialog.dart';
import '../../../utils/message_utils.dart';
import '../../../widgets/fab_styles.dart';
import '../../widgets/index.dart';
import '../widgets/weight_record_card.dart';

class CattleWeightRecordsTab extends StatefulWidget {
  final CattleWeightDetailController controller;

  const CattleWeightRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<CattleWeightRecordsTab> createState() => _CattleWeightRecordsTabState();
}

class _CattleWeightRecordsTabState extends State<CattleWeightRecordsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;



  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        if (!widget.controller.hasData) {
          return _buildEmptyState();
        }

        final filteredRecords = widget.controller.filteredRecords;

        return Scaffold(
          body: Column(
            children: [
              _buildControls(),
              Expanded(
                child: _buildRecordsList(filteredRecords),
              ),
            ],
          ),
          floatingActionButton: FabStyles.add(
            onPressed: _addWeightRecord,
            tooltip: 'Add Weight Record',
          ),
        );
      },
    );
  }

  Widget _buildControls() {
    return Padding(
      padding: EdgeInsets.all(_getResponsivePadding()),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 1,  // Equal space with sort widget
                child: DateRangeFilterWidget(
                  startDate: widget.controller.startDate,
                  endDate: widget.controller.endDate,
                  theme: DateRangeTheme.weight,
                  onRangeChanged: (start, end) {
                    widget.controller.setDateRange(start, end);
                  },
                  compact: true,
                  buttonHeight: 44,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 1,
                child: Container(
                  height: 44,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: widget.controller.sortBy,
                      hint: const Text('Sort by'),
                      isExpanded: true,
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      items: const [
                        DropdownMenuItem(value: 'Date', child: Text('Date')),
                        DropdownMenuItem(value: 'Weight', child: Text('Weight')),
                      ],
                      onChanged: (value) {
                        widget.controller.setSortBy(value);
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          if (widget.controller.startDate != null ||
              widget.controller.endDate != null ||
              widget.controller.sortBy != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Text(
                    'Showing ${widget.controller.filteredRecords.length} of ${widget.controller.allRecords.length} records',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => widget.controller.clearFilters(),
                    child: const Text('Clear Filters'),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRecordsList(List<WeightRecordIsar> records) {
    if (records.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text('No records found', style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Colors.grey[600])),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your date range or sort criteria',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: _getResponsivePadding()),
      itemCount: records.length,
      itemBuilder: (context, index) {
        final record = records[index];

        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: WeightRecordCard(
            record: record,
            cattle: widget.controller.cattle,
            onTap: () => _editWeightRecord(record),
            onEdit: () => _editWeightRecord(record),
            onDelete: () => _deleteWeightRecord(record),
            compact: false,
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Scaffold(
      body: Container(
        width: double.infinity,
        constraints: const BoxConstraints(minHeight: 300),
        margin: const EdgeInsets.fromLTRB(16, 16, 16, 32),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.purple.withAlpha(76)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: EmptyState.custom(
          icon: Icons.scale_outlined,
          title: 'No Weight Records',
          message: 'Add the first weight record for ${widget.controller.cattle.name}',
          action: EmptyState.createActionButton(
            text: 'Add First Record',
            onPressed: () => _addWeightRecord(),
            icon: Icons.add,
          ),
        ),
      ),
    );
  }

  // --- Action Handlers ---
  void _addWeightRecord() {
    WeightFormDialog.show(
      context: context,
      cattle: [widget.controller.cattle],
      preSelectedCattle: widget.controller.cattle,
      onRecordAdded: () => widget.controller.refresh(),
    );
  }

  void _editWeightRecord(WeightRecordIsar record) {
    WeightFormDialog.show(
      context: context,
      cattle: [widget.controller.cattle],
      existingRecord: record,
      onRecordAdded: () => widget.controller.refresh(),
    );
  }

  Future<void> _deleteWeightRecord(WeightRecordIsar record) async {
    // Robustness: Check for null businessId.
    final recordId = record.businessId;
    if (recordId == null) {
      MessageUtils.showError(context, 'Cannot delete record: Missing ID.');
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Weight Record'),
        content: Text(
          'Are you sure you want to delete this weight record?\n\n'
          'Weight: ${record.weight.toStringAsFixed(1)} kg\n'
          'Date: ${record.measurementDate != null ? DateFormat('MMM dd, yyyy').format(record.measurementDate!) : 'No date'}',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Cancel')),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await widget.controller.deleteRecord(recordId);
        if (mounted) {
          MessageUtils.showSuccess(context, 'Weight record deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          MessageUtils.showError(context, 'Error deleting record: $e');
        }
      }
    }
  }

  // --- Helper Methods ---







  double _getResponsivePadding() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0; // Mobile
    if (screenWidth < 1200) return 16.0; // Tablet
    return 20.0; // Desktop
  }
}