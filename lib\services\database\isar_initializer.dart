import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';

import 'isar_service.dart';
import 'database_helper.dart';
import '../../Dashboard/Farm Setup/services/farm_setup_handler.dart';
import '../../Dashboard/Farm Setup/models/farm_isar.dart';
import '../../Dashboard/Farm Setup/models/animal_type_isar.dart';
import '../../Dashboard/Farm Setup/models/breed_category_isar.dart';
import '../../Dashboard/Cattle/services/cattle_handler.dart';
import '../../Dashboard/Cattle/models/cattle_isar.dart';
import '../../Dashboard/Health/services/health_handler.dart';
import '../../Dashboard/Breeding/services/breeding_handler.dart';
import '../../Dashboard/Events/services/events_handler.dart';
import '../../Dashboard/Events/models/event_type_isar.dart';
import '../../Dashboard/Events/models/event_isar.dart';
import '../../Dashboard/Milk Records/services/milk_handler.dart';
import '../../Dashboard/Weight/services/weight_handler.dart';
import '../../Dashboard/Transactions/services/transactions_handler.dart';
import '../../Dashboard/Transactions/models/transaction_isar.dart';
import '../../Dashboard/Transactions/models/category_isar.dart';
import '../../Dashboard/Reports/services/reports_handler.dart';
import '../../Dashboard/Notifications/services/notifications_handler.dart';
import '../../constants/app_icons.dart';
import '../../Dashboard/Settings/services/settings_handler.dart';
import '../../Dashboard/widgets/dialogs/form_data_service.dart';

/// A utility class for initializing Isar database and registering handlers
class IsarInitializer {
  static final Logger _logger = Logger('IsarInitializer');
  static bool _isInitialized = false;

  /// Initialize Isar database and register all handlers
  static Future<void> initialize() async {
    if (_isInitialized) {
      _logger.info('Isar already initialized');
      return;
    }

    try {
      _logger.info('Initializing Isar...');

      final getIt = GetIt.instance;

      // Register IsarService as a singleton if not registered
      if (!getIt.isRegistered<IsarService>()) {
        _logger.info('Creating IsarService instance...');
        final isarService = await IsarService.instance;
        getIt.registerSingleton<IsarService>(isarService);
        _logger.info('IsarService registered successfully');
      }

      // Register and initialize database helper
      _logger.info('Initializing DatabaseHelper...');
      if (!getIt.isRegistered<DatabaseHelper>()) {
        final dbHelper = DatabaseHelper.instance;
        getIt.registerSingleton<DatabaseHelper>(dbHelper);
      }

      // Initialize the database helper
      final dbHelper = getIt<DatabaseHelper>();
      await dbHelper.init();
      _logger.info('DatabaseHelper initialized successfully');

      // After database helper is initialized, register other handlers if needed
      _logger.info('Registering service handlers...');
      _registerServiceHandlers(getIt);
      _logger.info('Service handlers registered successfully');

      // Fix animal type icons by resetting them
      await _fixAnimalTypeIcons();

      _isInitialized = true;
      _logger.info('Isar initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing Isar: $e');
      rethrow;
    }
  }

  /// Register all service handlers with GetIt
  static void _registerServiceHandlers(GetIt getIt) {
    if (!getIt.isRegistered<FarmSetupHandler>()) {
      getIt.registerSingleton<FarmSetupHandler>(FarmSetupHandler.instance);
    }
    if (!getIt.isRegistered<CattleHandler>()) {
      getIt.registerSingleton<CattleHandler>(CattleHandler.instance);
    }
    if (!getIt.isRegistered<HealthHandler>()) {
      getIt.registerSingleton<HealthHandler>(HealthHandler.instance);
    }
    if (!getIt.isRegistered<BreedingHandler>()) {
      getIt.registerSingleton<BreedingHandler>(BreedingHandler.instance);
    }
    if (!getIt.isRegistered<EventsHandler>()) {
      getIt.registerSingleton<EventsHandler>(EventsHandler.instance);
    }
    if (!getIt.isRegistered<MilkHandler>()) {
      getIt.registerSingleton<MilkHandler>(MilkHandler.instance);
    }
    if (!getIt.isRegistered<WeightHandler>()) {
      getIt.registerSingleton<WeightHandler>(WeightHandler.instance);
    }
    if (!getIt.isRegistered<TransactionsHandler>()) {
      getIt
          .registerSingleton<TransactionsHandler>(TransactionsHandler.instance);
    }
    if (!getIt.isRegistered<ReportsHandler>()) {
      getIt.registerSingleton<ReportsHandler>(ReportsHandler.instance);
    }
    if (!getIt.isRegistered<NotificationsHandler>()) {
      getIt.registerSingleton<NotificationsHandler>(
          NotificationsHandler.instance);
    }
    if (!getIt.isRegistered<SettingsHandler>()) {
      getIt.registerSingleton<SettingsHandler>(SettingsHandler.instance);
    }

    // Register FormDataService after all dependencies are registered
    FormDataServiceRegistration.registerServices();
    FormDataServiceRegistration.registerModularServices();
  }

  /// Ensure default data exists and create if needed
  static Future<void> ensureDefaultData() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final getIt = GetIt.instance;

      // Ensure default farm exists
      final farmHandler = getIt<FarmSetupHandler>();
      await _ensureDefaultFarm(farmHandler);

      // Ensure default animal types exist
      await _ensureDefaultAnimalTypes(farmHandler);

      // Ensure default animal stages exist
      // Temporarily commented out until AnimalStageIsar is properly generated
      // await _ensureDefaultAnimalStages(farmHandler);

      // Fix animal type icons if needed
      await _fixAnimalTypeIcons();

      // Ensure default breed categories exist
      await _ensureDefaultBreedCategories(farmHandler);

      // Ensure default event types exist
      await _ensureDefaultEventTypes(farmHandler);

      // Ensure default transaction categories exist
      await _ensureDefaultTransactionCategories(getIt<TransactionsHandler>());

      // Fix transaction category icons if needed
      await _fixTransactionCategoryIcons();

      // Ensure default settings exist
      await _ensureDefaultSettings(getIt<SettingsHandler>());

      _logger.info('Default data ensured successfully');
    } catch (e) {
      _logger.severe('Error ensuring default data: $e');
      rethrow;
    }
  }

  /// Ensure default farm exists
  static Future<void> _ensureDefaultFarm(FarmSetupHandler farmHandler) async {
    try {
      final farms = await farmHandler.getAllFarms();
      if (farms.isEmpty) {
        // Create a default farm
        final defaultFarm = FarmIsar()
          ..name = 'My Farm'
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();

        // Save the farm using a proper transaction
        final getIt = GetIt.instance;
        final isarService = getIt<IsarService>();

        // Wrap in a transaction
        await isarService.isar.writeTxn(() async {
          await isarService.farmIsars.put(defaultFarm);
        });

        _logger.info('Default farm created');
      }
    } catch (e) {
      _logger.severe('Error ensuring default farm: $e');
      // Don't rethrow to allow the initialization process to continue
    }
  }

  /// Ensure default animal types exist
  static Future<void> _ensureDefaultAnimalTypes(
      FarmSetupHandler farmHandler) async {
    try {
      debugPrint('======== CHECKING ANIMAL TYPES ========'); // Use debugPrint
      final types = await farmHandler.getAllAnimalTypes();
      debugPrint(
          'Found ${types.length} existing animal types'); // Use debugPrint

      if (types.isEmpty) {
        _logger.info('No animal types found, creating defaults');
        // Create and add default animal types with AppIcons
        final defaultTypes = [
          AnimalTypeIsar.create(
            name: 'Cow',
            icon: AppIcons.cow,
            color: AppIcons.getAnimalColor('Cow'),
            defaultGestationDays: 283,
            defaultHeatCycleDays: 21,
            defaultEmptyPeriodDays: 60,
            defaultBreedingAge: 450,
            businessId: AnimalTypeIsar.cowId,
          ),
          AnimalTypeIsar.create(
            name: 'Buffalo',
            icon: AppIcons.buffalo,
            color: AppIcons.getAnimalColor('Buffalo'),
            defaultGestationDays: 310,
            defaultHeatCycleDays: 21,
            defaultEmptyPeriodDays: 90,
            defaultBreedingAge: 730,
            businessId: AnimalTypeIsar.buffaloId,
          ),
          AnimalTypeIsar.create(
            name: 'Goat',
            icon: AppIcons.goat,
            color: AppIcons.getAnimalColor('Goat'),
            defaultGestationDays: 150,
            defaultHeatCycleDays: 21,
            defaultEmptyPeriodDays: 30,
            defaultBreedingAge: 240,
            businessId: AnimalTypeIsar.goatId,
          ),
          AnimalTypeIsar.create(
            name: 'Sheep',
            icon: AppIcons.sheep,
            color: AppIcons.getAnimalColor('Sheep'),
            defaultGestationDays: 152,
            defaultHeatCycleDays: 17,
            defaultEmptyPeriodDays: 45,
            defaultBreedingAge: 210,
            businessId: AnimalTypeIsar.sheepId,
          ),
          AnimalTypeIsar.create(
            name: 'Horse',
            icon: AppIcons.horse,
            color: AppIcons.getAnimalColor('Horse'),
            defaultGestationDays: 340,
            defaultHeatCycleDays: 21,
            defaultEmptyPeriodDays: 90,
            defaultBreedingAge: 730,
            businessId: AnimalTypeIsar.horseId,
          ),
        ];

        // Print details about created types before saving
        for (var animalType in defaultTypes) {
          debugPrint(
              'Default animal type created: ${animalType.name}'); // Use debugPrint
          debugPrint(
              '  - iconCodePoint: ${animalType.iconCodePoint}'); // Use debugPrint
          debugPrint(
              '  - iconFontFamily: ${animalType.iconFontFamily}'); // Use debugPrint
        }

        // Get the Isar service to save directly
        final getIt = GetIt.instance;
        final isarService = getIt<IsarService>();

        // Save all default animal types in a single transaction
        await isarService.isar.writeTxn(() async {
          for (final animalType in defaultTypes) {
            await isarService.animalTypeIsars.put(animalType);
          }
        });

        debugPrint(
            '======== DEFAULT ANIMAL TYPES CREATED ========'); // Use debugPrint
        _logger.info('Default animal types created successfully');
      } else {
        debugPrint(
            '======== EXISTING ANIMAL TYPES FOUND ========'); // Use debugPrint
        // Check and update existing animal types to ensure they have static IDs
        await _updateExistingTypesWithStaticIds(types);

        // Print details of existing animal types
        for (var animalType in types) {
          debugPrint(
              'Existing animal type: ${animalType.name}'); // Use debugPrint
          debugPrint(
              '  - businessId: ${animalType.businessId}'); // Use debugPrint
          debugPrint(
              '  - iconCodePoint: ${animalType.iconCodePoint}'); // Use debugPrint
          debugPrint(
              '  - iconFontFamily: ${animalType.iconFontFamily}'); // Use debugPrint
          debugPrint(
              '  - icon.codePoint: ${animalType.icon.codePoint}'); // Use debugPrint
          debugPrint(
              '  - icon.fontFamily: ${animalType.icon.fontFamily}'); // Use debugPrint
        }
        debugPrint(
            '======== END OF EXISTING ANIMAL TYPES ========'); // Use debugPrint
      }
    } catch (e) {
      _logger.severe('Error ensuring default animal types: $e');
      rethrow;
    }
  }

  /// Update existing animal types with static IDs to ensure consistency
  static Future<void> _updateExistingTypesWithStaticIds(List<AnimalTypeIsar> types) async {
    try {
      final getIt = GetIt.instance;
      final isarService = getIt<IsarService>();
      bool hasUpdates = false;

      await isarService.isar.writeTxn(() async {
        for (var type in types) {
          if (type.name != null) {
            final normalizedName = type.name!.toLowerCase();
            final staticId = AnimalTypeIsar.staticIds[normalizedName];

            // If this is a predefined type and its ID doesn't match the static ID, update it
            if (staticId != null && type.businessId != staticId) {
              _logger.info('Updating animal type ${type.name} with static ID: $staticId (old: ${type.businessId})');

              // Update cattle records that reference this type
              final cattleToUpdate = await isarService.isar.cattleIsars.buildQuery().findAll();

              final cattleNeedingUpdate = cattleToUpdate
                  .where((cattle) => cattle.animalTypeId == type.businessId)
                  .toList();

              for (var cattle in cattleNeedingUpdate) {
                cattle.animalTypeId = staticId;
                await isarService.isar.cattleIsars.put(cattle);
              }

              _logger.info('Updated ${cattleNeedingUpdate.length} cattle records with new animal type ID');

              // Update breeds that reference this type
              final breedsToUpdate = await isarService.isar.breedCategoryIsars.buildQuery().findAll();

              final breedsNeedingUpdate = breedsToUpdate
                  .where((breed) => breed.animalTypeId == type.businessId)
                  .toList();

              for (var breed in breedsNeedingUpdate) {
                breed.animalTypeId = staticId;
                await isarService.isar.breedCategoryIsars.put(breed);
              }

              _logger.info('Updated ${breedsNeedingUpdate.length} breed records with new animal type ID');

              // Update the animal type ID
              type.businessId = staticId;
              await isarService.animalTypeIsars.put(type);
              hasUpdates = true;
            }
          }
        }
      });

      if (hasUpdates) {
        _logger.info('Successfully updated animal types with static IDs');
      } else {
        _logger.info('No animal types needed ID updates');
      }
    } catch (e) {
      _logger.severe('Error updating animal types with static IDs: $e');
    }
  }

  /// Update existing breeds with static IDs to ensure consistency
  static Future<void> _updateExistingBreedsWithStaticIds(
      List<BreedCategoryIsar> breeds) async {
    try {
      final getIt = GetIt.instance;
      final isarService = getIt<IsarService>();
      bool hasUpdates = false;

      await isarService.isar.writeTxn(() async {
        for (var breed in breeds) {
          if (breed.name != null) {
            final normalizedName = breed.name!.toLowerCase();
            final staticId = BreedCategoryIsar.staticIds[normalizedName];

            // If this is a predefined breed and its ID doesn't match the static ID, update it
            if (staticId != null && breed.businessId != staticId) {
              _logger.info(
                  'Updating breed ${breed.name} with static ID: $staticId (old: ${breed.businessId})');

              // Update cattle records that reference this breed
              final cattleToUpdate = await isarService.isar.cattleIsars.buildQuery().findAll();

              final cattleNeedingUpdate = cattleToUpdate
                  .where((cattle) => cattle.breedId == breed.businessId)
                  .toList();

              for (var cattle in cattleNeedingUpdate) {
                cattle.breedId = staticId;
                await isarService.isar.cattleIsars.put(cattle);
              }

              _logger.info(
                  'Updated ${cattleNeedingUpdate.length} cattle records with new breed ID');

              // Update the breed ID
              breed.businessId = staticId;
              await isarService.breedCategoryIsars.put(breed);
              hasUpdates = true;
            }
          }
        }
      });

      if (hasUpdates) {
        _logger.info('Successfully updated breeds with static IDs');
      } else {
        _logger.info('No breeds needed ID updates');
      }
    } catch (e) {
      _logger.severe('Error updating breeds with static IDs: $e');
    }
  }

  /// Public method to specifically initialize breeds
  /// This can be called directly if the normal initialization process fails
  static Future<void> initializeDefaultBreeds() async {
    try {
      final getIt = GetIt.instance;
      final farmHandler = getIt<FarmSetupHandler>();

      // Ensure we have animal types first
      final animalTypes = await farmHandler.getAllAnimalTypes();
      if (animalTypes.isEmpty) {
        _logger.info('No animal types found, initializing animal types first');
        await _ensureDefaultAnimalTypes(farmHandler);
      }

      // Now initialize breeds
      await _ensureDefaultBreedCategories(farmHandler);
      _logger.info('Default breeds initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing default breeds: $e');
    }
  }

  /// Ensure default breed categories exist
  static Future<void> _ensureDefaultBreedCategories(
      FarmSetupHandler farmHandler) async {
    try {
      final breeds = await farmHandler.getAllBreedCategories();
      _logger.info('Found ${breeds.length} existing breed categories');

      // Get animal types for creating breeds
      final animalTypes = await farmHandler.getAllAnimalTypes();

      if (breeds.isEmpty) {
        _logger.info('No breed categories found, creating defaults');

        final getIt = GetIt.instance;
        final isarService = getIt<IsarService>();

        // Map to store animal types by name for easier lookup
        final animalTypeMap = <String, AnimalTypeIsar>{};
        for (final type in animalTypes) {
          if (type.name != null) {
            animalTypeMap[type.name!.toLowerCase()] = type;
          }
        }

        // Define all predefined breeds with descriptions, organized by animal type
        final allBreeds = <BreedCategoryIsar>[];

        // COW BREEDS - Just 2 breeds with same color
        if (animalTypeMap.containsKey('cow')) {
          final cowType = animalTypeMap['cow']!;
          const cowColor = Colors.blue;
          allBreeds.addAll([
            BreedCategoryIsar.create(
              name: 'Holstein',
              animalTypeId: cowType.businessId!,
              description:
                  'Black and white dairy breed known for high milk production',
              icon: Icons.pets,
              color: cowColor,
              businessId: BreedCategoryIsar.holsteinId,
            ),
            BreedCategoryIsar.create(
              name: 'Angus',
              animalTypeId: cowType.businessId!,
              description:
                  'Popular beef breed known for high-quality marbled meat',
              icon: Icons.pets,
              color: cowColor,
              businessId: BreedCategoryIsar.angusId,
            ),
          ]);
        }

        // BUFFALO BREEDS - Just 2 breeds with same color
        if (animalTypeMap.containsKey('buffalo')) {
          final buffaloType = animalTypeMap['buffalo']!;
          const buffaloColor = Colors.black;
          allBreeds.addAll([
            BreedCategoryIsar.create(
              name: 'Murrah',
              animalTypeId: buffaloType.businessId!,
              description: 'Black dairy buffalo known for high milk yield',
              icon: AppIcons.buffalo,
              color: buffaloColor,
              businessId: BreedCategoryIsar.murrahId,
            ),
            BreedCategoryIsar.create(
              name: 'Nili-Ravi',
              animalTypeId: buffaloType.businessId!,
              description:
                  'Blue-black breed with wall eyes and high milk production',
              icon: AppIcons.buffalo,
              color: buffaloColor,
              businessId: BreedCategoryIsar.niliraviId,
            ),
          ]);
        }

        // GOAT BREEDS - Just 2 breeds with same color
        if (animalTypeMap.containsKey('goat')) {
          final goatType = animalTypeMap['goat']!;
          const goatColor = Colors.red;
          allBreeds.addAll([
            BreedCategoryIsar.create(
              name: 'Alpine',
              animalTypeId: goatType.businessId!,
              description: 'Medium-sized dairy goat with upright ears',
              icon: AppIcons.goat,
              color: goatColor,
              businessId: BreedCategoryIsar.alpineId,
            ),
            BreedCategoryIsar.create(
              name: 'Boer',
              animalTypeId: goatType.businessId!,
              description:
                  'Meat goat breed with distinctive white body and red head',
              icon: AppIcons.goat,
              color: goatColor,
              businessId: BreedCategoryIsar.boerId,
            ),
          ]);
        }

        // SHEEP BREEDS - Just 2 breeds with same color
        if (animalTypeMap.containsKey('sheep')) {
          final sheepType = animalTypeMap['sheep']!;
          const sheepColor = Colors.green;
          allBreeds.addAll([
            BreedCategoryIsar.create(
              name: 'Merino',
              animalTypeId: sheepType.businessId!,
              description: 'Fine wool breed with excellent fleece quality',
              icon: AppIcons.sheep,
              color: sheepColor,
              businessId: BreedCategoryIsar.merinoId,
            ),
            BreedCategoryIsar.create(
              name: 'Suffolk',
              animalTypeId: sheepType.businessId!,
              description: 'Meat breed with black face and legs',
              icon: AppIcons.sheep,
              color: sheepColor,
              businessId: BreedCategoryIsar.suffolkId,
            ),
          ]);
        }

        // HORSE BREEDS - Just 2 breeds with same color
        if (animalTypeMap.containsKey('horse')) {
          final horseType = animalTypeMap['horse']!;
          final horseColor = Colors.amber.shade800;
          allBreeds.addAll([
            BreedCategoryIsar.create(
              name: 'Arabian',
              animalTypeId: horseType.businessId!,
              description: 'Elegant breed known for endurance and intelligence',
              icon: AppIcons.horse,
              color: horseColor,
              businessId: BreedCategoryIsar.arabianId,
            ),
            BreedCategoryIsar.create(
              name: 'Quarter Horse',
              animalTypeId: horseType.businessId!,
              description: 'Popular American breed excellent for sprinting',
              icon: AppIcons.horse,
              color: horseColor,
              businessId: BreedCategoryIsar.quarterhorseId,
            ),
          ]);
        }

        // Save all breeds in a single transaction
        if (allBreeds.isNotEmpty) {
          _logger
              .info('Saving ${allBreeds.length} predefined breeds to database');

          await isarService.isar.writeTxn(() async {
            for (final breed in allBreeds) {
              await isarService.breedCategoryIsars.put(breed);
            }
          });

          _logger.info('Predefined breed categories created successfully');
        }
      } else {
        _logger.info(
            '${breeds.length} breed categories already exist, checking for ID updates');

        // Check and update existing breeds to ensure they have static IDs
        await _updateExistingBreedsWithStaticIds(breeds);
      }
    } catch (e) {
      _logger.severe('Error ensuring default breed categories: $e');
      rethrow;
    }
  }

  /// Ensure default event types exist
  static Future<void> _ensureDefaultEventTypes(
      FarmSetupHandler farmHandler) async {
    try {
      debugPrint('======== CHECKING EVENT TYPES ========'); // Use debugPrint
      final eventTypes = await farmHandler.getEventTypes();
      debugPrint(
          'Found ${eventTypes.length} existing event types'); // Use debugPrint

      if (eventTypes.isEmpty) {
        _logger.info('No event types found, creating defaults');

        // Create and add default event types
        final defaultEventTypes = [
          EventTypeIsar.create(
              name: 'Vaccination',
              description: 'Vaccination and immunization events',
              color: Colors.red),
          EventTypeIsar.create(
              name: 'Health Check',
              description: 'Regular health inspections and check-ups',
              color: Colors.green),
          EventTypeIsar.create(
              name: 'Breeding',
              description: 'Breeding and insemination events',
              color: Colors.blue),
          EventTypeIsar.create(
              name: 'Pregnancy Check',
              description: 'Pregnancy diagnosis and confirmation',
              color: Colors.purple),
          EventTypeIsar.create(
              name: 'Birth/Calving',
              description: 'Birth of calves and delivery events',
              color: Colors.amber),
          EventTypeIsar.create(
              name: 'Deworming',
              description: 'Parasite treatment and prevention',
              color: Colors.teal),
          EventTypeIsar.create(
              name: 'Weight Recording',
              description: 'Animal weight measurement and tracking',
              color: Colors.orange),
          EventTypeIsar.create(
              name: 'Milk Recording',
              description: 'Milk production records and analysis',
              color: Colors.lightBlue),
          EventTypeIsar.create(
              name: 'Veterinary Visit',
              description: 'Professional veterinary consultations',
              color: Colors.deepPurple),
          EventTypeIsar.create(
              name: 'Feed Change',
              description: 'Changes to feeding regimen or diet',
              color: Colors.brown),
          EventTypeIsar.create(
              name: 'Medication',
              description: 'Administration of medicines and treatments',
              color: Colors.indigo),
          EventTypeIsar.create(
              name: 'Purchase',
              description: 'Acquisition of new animals',
              color: Colors.green.shade800),
          EventTypeIsar.create(
              name: 'Sale',
              description: 'Sale or transfer of animals',
              color: Colors.red),
          EventTypeIsar.create(
              name: 'Transport',
              description: 'Movement of animals between locations',
              color: Colors.cyan),
          EventTypeIsar.create(
              name: 'Weaning',
              description:
                  'Transition of young animals from milk to solid food',
              color: Colors.deepOrange),
        ];

        // Print details about event types before saving
        for (var eventType in defaultEventTypes) {
          debugPrint(
              'Creating event type: ${eventType.name}'); // Use debugPrint
        }

        // Get the Isar service to save directly
        final getIt = GetIt.instance;
        final isarService = getIt<IsarService>();

        // Save all default event types in a single transaction
        await isarService.isar.writeTxn(() async {
          for (final eventType in defaultEventTypes) {
            await isarService.eventTypeIsars.put(eventType);
          }
        });

        debugPrint(
            '======== DEFAULT EVENT TYPES CREATED ========'); // Use debugPrint
        // Verify event types were created
        final createdTypes = await farmHandler.getEventTypes();
        debugPrint(
            'After creation: Found ${createdTypes.length} event types'); // Use debugPrint
        for (var type in createdTypes) {
          debugPrint('- ${type.name}: ${type.description}'); // Use debugPrint
        }

        _logger.info(
            '${defaultEventTypes.length} default event types created successfully');
      } else {
        debugPrint('Existing event types:'); // Use debugPrint
        for (var type in eventTypes) {
          debugPrint('- ${type.name}: ${type.description}'); // Use debugPrint
        }
        _logger.info(
            '${eventTypes.length} event types already exist, checking for ID updates');

        // Update existing event types to use static IDs
        await _updateEventTypesWithStaticIds(eventTypes);
      }
      debugPrint(
          '======== END OF EVENT TYPES CHECK ========'); // Use debugPrint
    } catch (e) {
      _logger.severe('Error ensuring default event types: $e');
      debugPrint('ERROR with event types: $e'); // Use debugPrint
      rethrow;
    }
  }

  /// Update existing event types with static IDs to ensure consistency
  static Future<void> _updateEventTypesWithStaticIds(
      List<EventTypeIsar> eventTypes) async {
    try {
      final getIt = GetIt.instance;
      final isarService = getIt<IsarService>();
      bool hasUpdates = false;

      await isarService.isar.writeTxn(() async {
        for (var eventType in eventTypes) {
          final normalizedName = eventType.name.toLowerCase();
          final staticId = EventTypeIsar.staticIds[normalizedName];

          // If this is a predefined event type and its ID doesn't match the static ID, update it
          if (staticId != null && eventType.businessId != staticId) {
            _logger.info(
                'Updating event type ${eventType.name} with static ID: $staticId (old: ${eventType.businessId})');

            // Update events that reference this event type
            final eventsToUpdate = await isarService.isar.eventIsars.buildQuery().findAll();

            final eventsNeedingUpdate = eventsToUpdate
                .where((event) => event.type?.name == eventType.name)
                .toList();

            for (var event in eventsNeedingUpdate) {
              // Update the event type ID if needed
              if (event.type != null) {
                // Instead of trying to set businessId which doesn't exist,
                // we need to update the event type reference
                event.typeId = staticId;
                await isarService.isar.eventIsars.put(event);
              }
            }

            _logger.info(
                'Updated ${eventsNeedingUpdate.length} events with new event type ID');

            // Update the event type ID
            eventType.businessId = staticId;
            await isarService.eventTypeIsars.put(eventType);
            hasUpdates = true;
          }
        }
      });

      if (hasUpdates) {
        _logger.info('Successfully updated event types with static IDs');
      } else {
        _logger.info('No event types needed ID updates');
      }
    } catch (e) {
      _logger.severe('Error updating event types with static IDs: $e');
    }
  }

  /// Ensure default transaction categories exist
  static Future<void> _ensureDefaultTransactionCategories(
      TransactionsHandler transactionsHandler) async {
    try {
      final categories =
          await transactionsHandler.getAllTransactionCategories();
      if (categories.isEmpty) {
        _logger.info('No transaction categories found, creating defaults');

        // Create default income categories
        final incomeCategories = [
          CategoryIsar.create(
            categoryId:
                CategoryIsar.staticIds['milk sales'] ?? const Uuid().v4(),
            name: 'Milk Sales',
            description: 'Income from milk sales',
            type: 'Income',
            icon: AppIcons.milk,
          ),
          CategoryIsar.create(
            categoryId:
                CategoryIsar.staticIds['animal sales'] ?? const Uuid().v4(),
            name: 'Animal Sales',
            description: 'Income from selling animals',
            type: 'Income',
            icon: AppIcons.income,
          ),
          CategoryIsar.create(
            categoryId:
                CategoryIsar.staticIds['other income'] ?? const Uuid().v4(),
            name: 'Other Income',
            description: 'Other farm income sources',
            type: 'Income',
            icon: AppIcons.money,
          ),
        ];

        // Create default expense categories
        final expenseCategories = [
          CategoryIsar.create(
            categoryId: CategoryIsar.staticIds['feed'] ?? const Uuid().v4(),
            name: 'Feed',
            description: 'Expenses for animal feed',
            type: 'Expense',
            icon: AppIcons.grass,
          ),
          CategoryIsar.create(
            categoryId:
                CategoryIsar.staticIds['veterinary'] ?? const Uuid().v4(),
            name: 'Veterinary',
            description: 'Expenses for veterinary services',
            type: 'Expense',
            icon: AppIcons.medicine,
          ),
          CategoryIsar.create(
            categoryId:
                CategoryIsar.staticIds['supplies'] ?? const Uuid().v4(),
            name: 'Supplies',
            description: 'Farm supplies and equipment',
            type: 'Expense',
            icon: AppIcons.store,
          ),
          CategoryIsar.create(
            categoryId:
                CategoryIsar.staticIds['labor'] ?? const Uuid().v4(),
            name: 'Labor',
            description: 'Labor and staffing costs',
            type: 'Expense',
            icon: AppIcons.people,
          ),
          CategoryIsar.create(
            categoryId:
                CategoryIsar.staticIds['other expenses'] ?? const Uuid().v4(),
            name: 'Other Expenses',
            description: 'Miscellaneous farm expenses',
            type: 'Expense',
            icon: AppIcons.expense,
          ),
        ];

        // Get the Isar service to save directly
        final getIt = GetIt.instance;
        final isarService = getIt<IsarService>();

        // Combine all categories
        final allCategories = [...incomeCategories, ...expenseCategories];

        // Save all categories in a single transaction
        await isarService.isar.writeTxn(() async {
          for (final category in allCategories) {
            await isarService.categoryIsars.put(category);
          }
        });

        _logger.info('Default transaction categories created successfully');
      } else {
        _logger.info(
            'Transaction categories already exist, checking for ID updates');

        // Update existing categories to use static IDs where applicable
        await _updateTransactionCategoriesWithStaticIds(categories);
      }
    } catch (e) {
      _logger.severe('Error ensuring default transaction categories: $e');
      rethrow;
    }
  }

  /// Update existing transaction categories with static IDs
  static Future<void> _updateTransactionCategoriesWithStaticIds(
      List<CategoryIsar> categories) async {
    try {
      final getIt = GetIt.instance;
      final isarService = getIt<IsarService>();
      bool hasUpdates = false;

      await isarService.isar.writeTxn(() async {
        for (var category in categories) {
          final normalizedName = category.name.toLowerCase();
          final staticId = CategoryIsar.staticIds[normalizedName];

          if (staticId != null && category.categoryId != staticId) {
            _logger.info(
                'Updating category ${category.name} with static ID: $staticId (old: ${category.categoryId})');

            // Update transactions that reference this category
            final transactionsToUpdate = await isarService.isar.transactionIsars.buildQuery().findAll();

            // Filter manually
            final transactionsNeedingUpdate = transactionsToUpdate
                .where((transaction) => transaction.category == category.name)
                .toList();

            for (var transaction in transactionsNeedingUpdate) {
              transaction.category = category.name;
              await isarService.isar.transactionIsars.put(transaction);
            }

            _logger.info(
                'Updated ${transactionsNeedingUpdate.length} transactions with new category ID');

            // Update the category ID
            category.categoryId = staticId;
            await isarService.categoryIsars.put(category);
            hasUpdates = true;
          }
        }
      });

      if (hasUpdates) {
        _logger.info(
            'Successfully updated transaction categories with static IDs');
      } else {
        _logger.info('No transaction categories needed ID updates');
      }
    } catch (e) {
      _logger
          .severe('Error updating transaction categories with static IDs: $e');
    }
  }

  /// Ensure default settings exist
  static Future<void> _ensureDefaultSettings(
      SettingsHandler settingsHandler) async {
    try {
      // Handle default settings
      _logger.info('Checking default settings');
    } catch (e) {
      _logger.severe('Error ensuring default settings: $e');
      rethrow;
    }
  }

  /// Show a progress dialog while performing an operation
  static Future<T> showProgressDialog<T>({
    required BuildContext context,
    required String message,
    required Future<T> Function() operation,
  }) async {
    // Show the progress dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(message),
            ],
          ),
        );
      },
    );

    // Perform the operation
    try {
      final result = await operation();
      if (context.mounted) {
        // Hide the dialog
        Navigator.of(context).pop();
      }
      return result;
    } catch (e) {
      // Hide the dialog in case of error
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      rethrow;
    }
  }

  /// Clean up resources when the app is closing
  static Future<void> dispose() async {
    if (_isInitialized) {
      try {
        final getIt = GetIt.instance;
        final isarService = getIt<IsarService>();
        await isarService.close();

        _isInitialized = false;
        _logger.info('Isar disposed successfully');
      } catch (e) {
        _logger.severe('Error disposing Isar: $e');
      }
    }
  }

  /// Fix animal type icons by deleting and recreating them
  static Future<void> _fixAnimalTypeIcons() async {
    try {
      final getIt = GetIt.instance;
      final farmHandler = getIt<FarmSetupHandler>();
      final isarService = getIt<IsarService>();

      // Get existing animal types
      final existingTypes = await farmHandler.getAllAnimalTypes();

      // Force fix to update colors
      debugPrint(
          '⚠️ FIXING ANIMAL TYPE COLORS - THIS WILL RESET DEFAULT TYPES ⚠️'); // Use debugPrint

      // We need to save non-default animal types to restore later
      final customTypes = existingTypes
          .where((type) =>
              type.name != 'Cow' &&
              type.name != 'Buffalo' &&
              type.name != 'Goat' &&
              type.name != 'Sheep' &&
              type.name != 'Horse')
          .toList();

      // First delete all animal types - careful with relationships
      await isarService.isar.writeTxn(() async {
        await isarService.animalTypeIsars.clear();
      });

      // Now recreate the default types with correct icons using AppIcons
      final defaultTypes = [
        AnimalTypeIsar.create(
          name: 'Cow',
          icon: AppIcons.cow,
          color: AppIcons.getAnimalColor('Cow'),
          defaultGestationDays: 283,
          defaultHeatCycleDays: 21,
          defaultEmptyPeriodDays: 60,
          defaultBreedingAge: 450,
        ),
        AnimalTypeIsar.create(
          name: 'Buffalo',
          icon: AppIcons.buffalo,
          color: AppIcons.getAnimalColor('Buffalo'),
          defaultGestationDays: 310,
          defaultHeatCycleDays: 21,
          defaultEmptyPeriodDays: 90,
          defaultBreedingAge: 730,
        ),
        AnimalTypeIsar.create(
          name: 'Goat',
          icon: AppIcons.goat,
          color: AppIcons.getAnimalColor('Goat'),
          defaultGestationDays: 150,
          defaultHeatCycleDays: 21,
          defaultEmptyPeriodDays: 30,
          defaultBreedingAge: 240,
        ),
        AnimalTypeIsar.create(
          name: 'Sheep',
          icon: AppIcons.sheep,
          color: AppIcons.getAnimalColor('Sheep'),
          defaultGestationDays: 152,
          defaultHeatCycleDays: 17,
          defaultEmptyPeriodDays: 45,
          defaultBreedingAge: 210,
        ),
        AnimalTypeIsar.create(
          name: 'Horse',
          icon: AppIcons.horse,
          color: AppIcons.getAnimalColor('Horse'),
          defaultGestationDays: 340,
          defaultHeatCycleDays: 21,
          defaultEmptyPeriodDays: 90,
          defaultBreedingAge: 730,
        ),
      ];

      // Save all default and custom types
      await isarService.isar.writeTxn(() async {
        for (final animalType in [...defaultTypes, ...customTypes]) {
          await isarService.animalTypeIsars.put(animalType);
        }
      });

      debugPrint(
          '✅ DEFAULT ANIMAL TYPES RESET WITH CORRECT COLORS ✅'); // Use debugPrint

      // Log the fixed colors
      final fixedTypes = await farmHandler.getAllAnimalTypes();
      for (var type in fixedTypes) {
        debugPrint('Fixed animal type: ${type.name}'); // Use debugPrint
        debugPrint('  - Color: ${type.color}'); // Use debugPrint
        debugPrint('  - ColorValue: ${type.colorValue}'); // Use debugPrint
      }
    } catch (e) {
      debugPrint('Error fixing animal types: $e'); // Use debugPrint
      rethrow;
    }
  }

  /// Fix transaction category icons by deleting and recreating them
  static Future<void> _fixTransactionCategoryIcons() async {
    try {
      final getIt = GetIt.instance;
      final transactionsHandler = getIt<TransactionsHandler>();
      final isarService = getIt<IsarService>();

      // Get existing categories
      final existingCategories =
          await transactionsHandler.getAllTransactionCategories();

      // Keep track of custom categories
      final customCategories = existingCategories
          .where((cat) =>
              !CategoryIsar.staticIds.containsKey(cat.name.toLowerCase()))
          .toList();

      await isarService.isar.writeTxn(() async {
        await isarService.categoryIsars.clear();
      });

      // Create default categories with fixed icons
      final incomeCategories = [
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['milk sales'] ?? const Uuid().v4(),
          name: 'Milk Sales',
          description: 'Income from milk sales',
          type: 'Income',
          icon: AppIcons.milk,
        ),
        CategoryIsar.create(
          categoryId:
              CategoryIsar.staticIds['animal sales'] ?? const Uuid().v4(),
          name: 'Animal Sales',
          description: 'Income from selling animals',
          type: 'Income',
          icon: AppIcons.income,
        ),
        CategoryIsar.create(
          categoryId:
              CategoryIsar.staticIds['other income'] ?? const Uuid().v4(),
          name: 'Other Income',
          description: 'Other farm income sources',
          type: 'Income',
          icon: AppIcons.money,
        ),
      ];

      // Create default expense categories
      final expenseCategories = [
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['feed'] ?? const Uuid().v4(),
          name: 'Feed',
          description: 'Expenses for animal feed',
          type: 'Expense',
          icon: AppIcons.grass,
        ),
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['veterinary'] ?? const Uuid().v4(),
          name: 'Veterinary',
          description: 'Expenses for veterinary services',
          type: 'Expense',
          icon: AppIcons.medicine,
        ),
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['supplies'] ?? const Uuid().v4(),
          name: 'Supplies',
          description: 'Farm supplies and equipment',
          type: 'Expense',
          icon: AppIcons.store,
        ),
        CategoryIsar.create(
          categoryId: CategoryIsar.staticIds['labor'] ?? const Uuid().v4(),
          name: 'Labor',
          description: 'Labor and staffing costs',
          type: 'Expense',
          icon: AppIcons.people,
        ),
        CategoryIsar.create(
          categoryId:
              CategoryIsar.staticIds['other expenses'] ?? const Uuid().v4(),
          name: 'Other Expenses',
          description: 'Miscellaneous farm expenses',
          type: 'Expense',
          icon: AppIcons.expense,
        ),
      ];

      final defaultCategories = [...incomeCategories, ...expenseCategories];

      // Save all categories in a single transaction
      await isarService.isar.writeTxn(() async {
        for (final category in [...defaultCategories, ...customCategories]) {
          await isarService.categoryIsars.put(category);
        }
      });

      _logger.info('Transaction category icons fixed');
    } catch (e) {
      _logger.severe('Error fixing transaction category icons: $e');
      rethrow;
    }
  }
}


